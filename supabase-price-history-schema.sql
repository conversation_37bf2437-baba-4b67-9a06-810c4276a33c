-- Price History Schema for Ingredient Cost Tracking
-- Run this in your Supabase SQL Editor

-- Create price_history table to track cost changes over time
CREATE TABLE IF NOT EXISTS price_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ingredient_id UUID REFERENCES ingredients(id) ON DELETE CASCADE,
    cost_per_unit DECIMAL(10,2) NOT NULL,
    supplier TEXT,
    purchase_date DATE NOT NULL DEFAULT CURRENT_DATE,
    quantity_purchased DECIMAL(10,2) DEFAULT 0,
    total_cost DECIMAL(10,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_price_history_ingredient_id ON price_history(ingredient_id);
CREATE INDEX IF NOT EXISTS idx_price_history_purchase_date ON price_history(purchase_date);
CREATE INDEX IF NOT EXISTS idx_price_history_supplier ON price_history(supplier);

-- Create a function to automatically update the ingredients table with latest price
CREATE OR REPLACE FUNCTION update_ingredient_latest_price()
RET<PERSON>NS TRIGGER AS $$
BEGIN
    -- Update the ingredient's cost_per_unit with the latest price
    UPDATE ingredients 
    SET 
        cost_per_unit = NEW.cost_per_unit,
        updated_at = NOW()
    WHERE id = NEW.ingredient_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update ingredient price when new price history is added
DROP TRIGGER IF EXISTS trigger_update_ingredient_price ON price_history;
CREATE TRIGGER trigger_update_ingredient_price
    AFTER INSERT ON price_history
    FOR EACH ROW
    EXECUTE FUNCTION update_ingredient_latest_price();

-- Function to get price statistics for an ingredient
CREATE OR REPLACE FUNCTION get_ingredient_price_stats(
    ingredient_uuid UUID,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE(
    lowest_price DECIMAL(10,2),
    highest_price DECIMAL(10,2),
    average_price DECIMAL(10,2),
    latest_price DECIMAL(10,2),
    price_trend TEXT,
    total_purchases INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH price_data AS (
        SELECT 
            cost_per_unit,
            purchase_date,
            ROW_NUMBER() OVER (ORDER BY purchase_date DESC) as rn
        FROM price_history 
        WHERE ingredient_id = ingredient_uuid 
        AND purchase_date >= CURRENT_DATE - INTERVAL '%s days' % days_back
    ),
    stats AS (
        SELECT 
            MIN(cost_per_unit) as min_price,
            MAX(cost_per_unit) as max_price,
            AVG(cost_per_unit) as avg_price,
            COUNT(*) as purchase_count
        FROM price_data
    ),
    latest AS (
        SELECT cost_per_unit as current_price
        FROM price_data 
        WHERE rn = 1
    ),
    trend_calc AS (
        SELECT 
            CASE 
                WHEN COUNT(*) < 2 THEN 'insufficient_data'
                WHEN (SELECT cost_per_unit FROM price_data WHERE rn = 1) > 
                     (SELECT cost_per_unit FROM price_data WHERE rn = 2) THEN 'increasing'
                WHEN (SELECT cost_per_unit FROM price_data WHERE rn = 1) < 
                     (SELECT cost_per_unit FROM price_data WHERE rn = 2) THEN 'decreasing'
                ELSE 'stable'
            END as trend
        FROM price_data
    )
    SELECT 
        COALESCE(s.min_price, 0) as lowest_price,
        COALESCE(s.max_price, 0) as highest_price,
        COALESCE(s.avg_price, 0) as average_price,
        COALESCE(l.current_price, 0) as latest_price,
        COALESCE(t.trend, 'no_data') as price_trend,
        COALESCE(s.purchase_count, 0) as total_purchases
    FROM stats s
    CROSS JOIN latest l
    CROSS JOIN trend_calc t;
END;
$$ LANGUAGE plpgsql;

-- Function to get weekly price summary
CREATE OR REPLACE FUNCTION get_weekly_price_summary(
    ingredient_uuid UUID DEFAULT NULL
)
RETURNS TABLE(
    ingredient_id UUID,
    ingredient_name TEXT,
    week_start DATE,
    lowest_price DECIMAL(10,2),
    highest_price DECIMAL(10,2),
    average_price DECIMAL(10,2),
    purchase_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id as ingredient_id,
        i.name as ingredient_name,
        DATE_TRUNC('week', ph.purchase_date)::DATE as week_start,
        MIN(ph.cost_per_unit) as lowest_price,
        MAX(ph.cost_per_unit) as highest_price,
        AVG(ph.cost_per_unit) as average_price,
        COUNT(*)::INTEGER as purchase_count
    FROM ingredients i
    JOIN price_history ph ON i.id = ph.ingredient_id
    WHERE (ingredient_uuid IS NULL OR i.id = ingredient_uuid)
    AND ph.purchase_date >= CURRENT_DATE - INTERVAL '4 weeks'
    GROUP BY i.id, i.name, DATE_TRUNC('week', ph.purchase_date)
    ORDER BY week_start DESC, i.name;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on price_history table
ALTER TABLE price_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for price_history
DROP POLICY IF EXISTS "Authenticated users can read price_history" ON price_history;
CREATE POLICY "Authenticated users can read price_history" ON price_history
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Service role can manage price_history" ON price_history;
CREATE POLICY "Service role can manage price_history" ON price_history
    FOR ALL USING (auth.role() = 'service_role');

-- Insert some sample price history data
DO $$
DECLARE
    chicken_id UUID;
    rice_id UUID;
    soy_sauce_id UUID;
BEGIN
    -- Get ingredient IDs (assuming they exist from previous schema)
    SELECT id INTO chicken_id FROM ingredients WHERE name = 'Chicken Breast' LIMIT 1;
    SELECT id INTO rice_id FROM ingredients WHERE name = 'Jasmine Rice' LIMIT 1;
    SELECT id INTO soy_sauce_id FROM ingredients WHERE name = 'Soy Sauce' LIMIT 1;
    
    -- Insert price history for Chicken Breast (showing price fluctuation)
    IF chicken_id IS NOT NULL THEN
        INSERT INTO price_history (ingredient_id, cost_per_unit, supplier, purchase_date, quantity_purchased, total_cost, notes) VALUES
        (chicken_id, 8.99, 'Fresh Farm Produce', CURRENT_DATE - INTERVAL '21 days', 25.0, 224.75, 'Regular weekly order'),
        (chicken_id, 7.50, 'Wholesale Meats Co.', CURRENT_DATE - INTERVAL '14 days', 30.0, 225.00, 'Found better supplier'),
        (chicken_id, 7.75, 'Wholesale Meats Co.', CURRENT_DATE - INTERVAL '7 days', 25.0, 193.75, 'Slight price increase'),
        (chicken_id, 7.25, 'Wholesale Meats Co.', CURRENT_DATE - INTERVAL '3 days', 20.0, 145.00, 'Bulk discount applied'),
        (chicken_id, 8.10, 'Fresh Farm Produce', CURRENT_DATE, 15.0, 121.50, 'Emergency restock - higher price');
    END IF;
    
    -- Insert price history for Jasmine Rice (more stable pricing)
    IF rice_id IS NOT NULL THEN
        INSERT INTO price_history (ingredient_id, cost_per_unit, supplier, purchase_date, quantity_purchased, total_cost, notes) VALUES
        (rice_id, 2.99, 'Asian Grocery Co.', CURRENT_DATE - INTERVAL '28 days', 50.0, 149.50, 'Monthly bulk order'),
        (rice_id, 2.85, 'Asian Grocery Co.', CURRENT_DATE - INTERVAL '14 days', 50.0, 142.50, 'Volume discount'),
        (rice_id, 2.95, 'Asian Grocery Co.', CURRENT_DATE - INTERVAL '7 days', 25.0, 73.75, 'Regular restock');
    END IF;
    
    -- Insert price history for Soy Sauce
    IF soy_sauce_id IS NOT NULL THEN
        INSERT INTO price_history (ingredient_id, cost_per_unit, supplier, purchase_date, quantity_purchased, total_cost, notes) VALUES
        (soy_sauce_id, 3.49, 'Asian Grocery Co.', CURRENT_DATE - INTERVAL '21 days', 12.0, 41.88, 'Regular order'),
        (soy_sauce_id, 3.25, 'Restaurant Supply Plus', CURRENT_DATE - INTERVAL '10 days', 15.0, 48.75, 'New supplier trial'),
        (soy_sauce_id, 3.15, 'Restaurant Supply Plus', CURRENT_DATE - INTERVAL '2 days', 10.0, 31.50, 'Confirmed better pricing');
    END IF;
    
    RAISE NOTICE 'Sample price history data inserted successfully!';
END $$;
