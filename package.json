{"private": true, "scripts": {"dev": "next dev --turbopack", "dev:square": "node scripts/start-with-square.js", "setup:square": "node scripts/setup-square.js", "build": "next build", "start": "next start", "prettier": "prettier --write --ignore-unknown ."}, "dependencies": {"@heroicons/react": "2.2.0", "@mdx-js/loader": "3.1.0", "@mdx-js/react": "3.1.0", "@next/mdx": "15.4.0-canary.70", "@supabase/ssr": "0.6.1", "@supabase/supabase-js": "2.50.5", "@types/mdx": "2.0.13", "clsx": "2.1.1", "codehike": "1.0.7", "date-fns": "4.1.0", "dinero.js": "2.0.0-alpha.10", "dotenv": "17.2.0", "ms": "3.0.0-canary.1", "next": "15.4.0-canary.70", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "2.15.4", "recma-codehike": "0.0.1", "remark-codehike": "0.0.1", "server-only": "0.0.1", "squareup": "1.0.0", "styled-components": "6.1.15", "swagger-ui-react": "5.27.0", "use-count-up": "3.0.1", "zod": "3.24.3"}, "devDependencies": {"@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.4", "@tailwindcss/typography": "0.5.16", "@types/ms": "2.1.0", "@types/node": "22.13.1", "@types/react": "19.1.2", "@types/react-dom": "19.0.3", "eslint": "9.31.0", "eslint-config-next": "15.4.3", "postcss": "8.5.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "tailwindcss": "4.1.4", "typescript": "5.7.3", "vercel": "44.5.5"}, "overrides": {"swagger-ui-react": {"react": "$react", "react-dom": "$react-dom"}}}