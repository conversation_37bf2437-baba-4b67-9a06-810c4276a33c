-- Simple repair script - just the essentials for authentication
-- Run this to create the minimum required tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table (the most important one)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT,
    role TEXT NOT NULL DEFAULT 'viewer',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_role CHECK (role IN ('admin', 'staff', 'viewer'))
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'role', 'viewer')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create basic inventory tables
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'Rice',
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    stock INTEGER NOT NULL DEFAULT 0,
    min_stock INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS ingredients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL DEFAULT 'All',
    unit TEXT NOT NULL DEFAULT 'units',
    stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    min_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
    supplier TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS cached_products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    square_catalog_id TEXT,
    name TEXT NOT NULL,
    category TEXT,
    price BIGINT DEFAULT 0,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS sales_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    location_id TEXT NOT NULL,
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE cached_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_metrics ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user role
DROP FUNCTION IF EXISTS get_user_role();
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role
        FROM user_profiles
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Basic RLS Policies
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Service role can manage user profiles" ON user_profiles;
CREATE POLICY "Service role can manage user profiles" ON user_profiles
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Authenticated users can read products" ON products;
CREATE POLICY "Authenticated users can read products" ON products
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Service role can manage products" ON products;
CREATE POLICY "Service role can manage products" ON products
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Authenticated users can read ingredients" ON ingredients;
CREATE POLICY "Authenticated users can read ingredients" ON ingredients
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Service role can manage ingredients" ON ingredients;
CREATE POLICY "Service role can manage ingredients" ON ingredients
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Authenticated users can read cached_products" ON cached_products;
CREATE POLICY "Authenticated users can read cached_products" ON cached_products
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Service role can manage cached_products" ON cached_products;
CREATE POLICY "Service role can manage cached_products" ON cached_products
    FOR ALL USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Admins can read sales_metrics" ON sales_metrics;
CREATE POLICY "Admins can read sales_metrics" ON sales_metrics
    FOR SELECT USING (get_user_role() = 'admin');

DROP POLICY IF EXISTS "Service role can manage sales_metrics" ON sales_metrics;
CREATE POLICY "Service role can manage sales_metrics" ON sales_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- Insert some basic sample data (without ON CONFLICT)
DO $$
BEGIN
    -- Only insert if table is empty
    IF NOT EXISTS (SELECT 1 FROM products LIMIT 1) THEN
        INSERT INTO products (name, category, price, cost, stock, min_stock, description, status) VALUES
        ('Chicken Fried Rice', 'Rice', 13.99, 4.80, 18, 5, 'Classic fried rice with tender chicken and fresh vegetables', 'active'),
        ('Vegetable Fried Rice', 'Rice', 11.99, 3.50, 22, 5, 'Healthy vegetarian fried rice with seasonal vegetables', 'active'),
        ('Pad Thai', 'Noodles', 14.99, 5.20, 15, 5, 'Traditional Thai stir-fried noodles with tamarind sauce', 'active'),
        ('Grilled Salmon', 'Entree', 22.99, 12.50, 8, 3, 'Fresh Atlantic salmon grilled to perfection', 'active'),
        ('Iced Coffee', 'Drinks', 3.99, 1.20, 50, 20, 'Freshly brewed coffee served over ice', 'active');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM ingredients LIMIT 1) THEN
        INSERT INTO ingredients (name, category, unit, stock, min_stock, cost_per_unit, supplier) VALUES
        ('Jasmine Rice', 'Dry', 'lbs', 50.0, 20.0, 2.99, 'Asian Grocery Co.'),
        ('Chicken Breast', 'Meat', 'lbs', 25.0, 10.0, 8.99, 'Fresh Farm Produce'),
        ('Soy Sauce', 'Liquid', 'bottles', 12.0, 5.0, 3.49, 'Asian Grocery Co.'),
        ('Green Onions', 'Bag', 'bunches', 8.0, 5.0, 1.99, 'Fresh Farm Produce'),
        ('Black Pepper', 'Dry', 'oz', 12.0, 8.0, 0.99, 'Spice World');
    END IF;
END $$;
