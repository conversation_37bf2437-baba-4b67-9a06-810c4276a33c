-- Suppliers Schema
-- Run this in your Supabase SQL Editor

-- Create suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    notes TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active);

-- Function to update supplier updated_at timestamp
CREATE OR REPLACE FUNCTION update_supplier_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updating supplier timestamp
DROP TRIGGER IF EXISTS update_suppliers_updated_at ON suppliers;
CREATE TRIGGER update_suppliers_updated_at
    BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_supplier_updated_at();

-- Enable Row Level Security
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for suppliers
DROP POLICY IF EXISTS "Authenticated users can read suppliers" ON suppliers;
CREATE POLICY "Authenticated users can read suppliers" ON suppliers
    FOR SELECT USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Service role can manage suppliers" ON suppliers;
CREATE POLICY "Service role can manage suppliers" ON suppliers
    FOR ALL USING (auth.role() = 'service_role');

-- Insert some sample suppliers
INSERT INTO suppliers (name, contact_person, email, phone, address, notes) VALUES
('Fresh Farm Produce', 'John Smith', '<EMAIL>', '(*************', '123 Farm Road, Valley City', 'Reliable supplier for fresh ingredients'),
('Asian Grocery Co.', 'Lisa Chen', '<EMAIL>', '(*************', '456 Market Street, Chinatown', 'Best prices for Asian ingredients'),
('Wholesale Meats Co.', 'Mike Johnson', '<EMAIL>', '(*************', '789 Industrial Blvd, Meat District', 'High quality meats, bulk discounts'),
('Restaurant Supply Plus', 'Sarah Davis', '<EMAIL>', '(*************', '321 Supply Avenue, Business Park', 'One-stop shop for restaurant supplies'),
('Spice World', 'David Kumar', '<EMAIL>', '(*************', '654 Spice Lane, Flavor Town', 'Premium spices and seasonings'),
('Local Dairy Farm', 'Emma Wilson', '<EMAIL>', '(*************', '987 Dairy Road, Countryside', 'Fresh dairy products, local sourcing')
ON CONFLICT (name) DO NOTHING;
