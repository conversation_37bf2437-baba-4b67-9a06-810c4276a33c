import '#/styles/globals.css';

import { InventoryNav } from '#/ui/inventory-nav';
import { AuthProvider } from '#/lib/auth-context';
import { Metadata } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';

const geistSans = Geist({ variable: '--font-geist-sans', subsets: ['latin'] });

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: { default: 'Inventory Management', template: '%s | Inventory Management' },
  description: 'Manage your restaurant inventory, products, and ingredients efficiently.',
  openGraph: {
    title: 'Inventory Management System',
    description: 'Manage your restaurant inventory, products, and ingredients efficiently.',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full bg-gray-50">
      <body className={`h-full font-sans ${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AuthProvider>
          <div className="min-h-full">
            <InventoryNav />

            <main className="py-10">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                {children}
              </div>
            </main>
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
