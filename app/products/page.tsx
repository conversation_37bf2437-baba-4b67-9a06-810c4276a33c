'use client';

import { useState, useEffect } from 'react';
import { Boundary } from '#/ui/boundary';
import { Button } from '#/ui/button';
import { SyncButton } from '#/ui/sync-button';
import { productsService, Product } from '#/lib/products-service';
import { EditProductModal } from '#/components/products/edit-product-modal';

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const data = await productsService.getAllProducts();

      if (data && data.length > 0) {
        console.log('Using database data');
        setProducts(data);
        setFilteredProducts(data);
      } else {
        console.log('No products found in database');
        setProducts([]);
        setFilteredProducts([]);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
      setFilteredProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim() === '') {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(term.toLowerCase()) ||
        product.category.toLowerCase().includes(term.toLowerCase()) ||
        product.description?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredProducts(filtered);
    }
  };



  const handleEditProduct = async (updatedProduct: Product) => {
    try {
      await productsService.updateProduct(updatedProduct.id, updatedProduct);
      setNotification({ type: 'success', message: `Successfully updated ${updatedProduct.name}!` });
      loadProducts(); // Refresh the list

      // Clear notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error('Error updating product:', error);
      setNotification({ type: 'error', message: 'Error updating product. Please try again.' });

      // Clear notification after 5 seconds for errors
      setTimeout(() => setNotification(null), 5000);
    }
  };



  const openEditModal = (product: Product) => {
    setEditingProduct(product);
    setIsEditModalOpen(true);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">All Products</h1>
          <div className="flex space-x-3">
            <div className="bg-gray-200 animate-pulse h-10 w-32 rounded"></div>
            <div className="bg-gray-200 animate-pulse h-10 w-24 rounded"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-gray-200 animate-pulse h-64 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {notification.message}
        </div>
      )}

      <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0">
        <h1 className="text-2xl font-bold text-gray-900">All Products</h1>

        {/* Search Bar */}
        <div className="flex flex-col space-y-3 md:flex-row md:space-y-0 md:space-x-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              onClick={loadProducts}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Refresh
            </Button>
            <SyncButton />
          </div>
        </div>
      </div>

      {filteredProducts.length === 0 && products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg mb-4">No products found</div>
          <div className="text-gray-400 mb-6">
            Sync products from Square POS to get started
          </div>
          <SyncButton />
        </div>
      ) : (
        <>
          {/* Search Results Info */}
          {searchTerm && (
            <div className="text-sm text-gray-600 mb-4">
              {filteredProducts.length === 0
                ? `No products found for "${searchTerm}"`
                : `Found ${filteredProducts.length} product${filteredProducts.length === 1 ? '' : 's'} for "${searchTerm}"`
              }
            </div>
          )}

          {/* Products Grid */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mb-6">
            {filteredProducts.map((product) => (
              <Boundary
                key={product.id}
                label={product.name}
                className="bg-white border-gray-200"
              >
                <div className="space-y-4">
                  {/* Product Header */}
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                      <p className="text-sm text-gray-500">{product.category}</p>
                      {product.square_catalog_id && (
                        <p className="text-xs text-blue-500">From Square POS</p>
                      )}
                    </div>

                  </div>

                  {/* Description */}
                  {product.description && (
                    <p className="text-sm text-gray-600">{product.description}</p>
                  )}





                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <button
                      onClick={() => openEditModal(product)}
                      className="w-full rounded-md px-2 py-1 text-xs font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              </Boundary>
            ))}
          </div>

          {/* Summary Stats */}
          <Boundary label="Products Summary">
            <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{products.length}</div>
                <div className="text-sm text-gray-500">Total Products</div>
              </div>


            </div>
          </Boundary>
        </>
      )}

      {/* Edit Product Modal */}
      <EditProductModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleEditProduct}
        product={editingProduct}
      />
    </div>
  );
}
