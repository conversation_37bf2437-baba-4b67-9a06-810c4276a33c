import { Boundary } from '#/ui/boundary';
import { Button } from '#/ui/button';
import { getProductsByCategory } from '#/lib/dummy-data';

export default function RiceProductsPage() {
  const riceProducts = getProductsByCategory('Rice');
  const lowStockCount = riceProducts.filter(p => p.stock <= p.minStock).length;



  return (
    <div className="space-y-6">
      {/* Category Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Total Rice Products</div>
          <div className="text-2xl font-bold text-blue-600">{riceProducts.length}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Low Stock Items</div>
          <div className="text-2xl font-bold text-red-600">{lowStockCount}</div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {riceProducts.map((product) => (
          <Boundary
            key={product.id}
            label={product.name}
            className={`bg-white ${
              product.stock <= product.minStock ? 'border-red-300 bg-red-50' : 'border-gray-200'
            }`}
          >
            <div className="space-y-4">
              {/* Product Header */}
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                  <p className="text-sm text-gray-500">{product.category}</p>
                </div>

              </div>

              {/* Description */}
              <p className="text-sm text-gray-600">{product.description}</p>

              {/* Stock Status */}
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">Stock: </span>
                  <span
                    className={`font-bold ${
                      product.stock <= product.minStock ? 'text-red-600' : 'text-green-600'
                    }`}
                  >
                    {product.stock}
                  </span>
                  <span className="text-sm text-gray-500"> (Min: {product.minStock})</span>
                </div>
                {product.stock <= product.minStock && (
                  <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">
                    Low Stock
                  </span>
                )}
              </div>



              {/* Ingredients */}
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Ingredients:</div>
                <div className="flex flex-wrap gap-1">
                  {product.ingredients.map((ingredient, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                  Edit
                </Button>
                <Button className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm">
                  View Recipe
                </Button>
              </div>
            </div>
          </Boundary>
        ))}
      </div>
    </div>
  );
}
