import { Boundary } from '#/ui/boundary';
import { Button } from '#/ui/button';

// Mock data for entree products
const entreeProducts = [
  {
    id: 12,
    name: 'Grilled Salmon',
    category: 'Entree',
    price: 22.99,
    cost: 9.50,
    stock: 8,
    minStock: 5,
    ingredients: ['Salmon Fillet', 'Lemon', 'Herbs', 'Olive Oil', 'Asparagus'],
    description: 'Fresh grilled salmon with seasonal vegetables',
    status: 'active',
  },
  {
    id: 13,
    name: 'Beef Steak',
    category: 'Entree',
    price: 28.99,
    cost: 12.00,
    stock: 6,
    minStock: 5,
    ingredients: ['Ribeye Steak', 'Garlic', 'Butter', 'Rosemary', 'Mashed Potatoes'],
    description: 'Premium ribeye steak with garlic butter',
    status: 'active',
  },
  {
    id: 14,
    name: 'Chicken Teriyaki',
    category: 'Entree',
    price: 18.99,
    cost: 6.80,
    stock: 15,
    minStock: 5,
    ingredients: ['Chicken Thigh', 'Teriyaki Sauce', 'Rice', '<PERSON><PERSON><PERSON><PERSON>'],
    description: 'Glazed chicken thigh with teriyaki sauce',
    status: 'active',
  },
  {
    id: 15,
    name: 'Pork Ribs',
    category: 'Entree',
    price: 24.99,
    cost: 8.50,
    stock: 4,
    minStock: 5,
    ingredients: ['Pork Ribs', 'BBQ Sauce', 'Coleslaw', 'Corn'],
    description: 'Slow-cooked BBQ pork ribs with sides',
    status: 'active',
  },
  {
    id: 16,
    name: 'Vegetarian Curry',
    category: 'Entree',
    price: 16.99,
    cost: 5.20,
    stock: 12,
    minStock: 5,
    ingredients: ['Mixed Vegetables', 'Coconut Milk', 'Curry Spices', 'Basmati Rice'],
    description: 'Rich vegetarian curry with coconut milk',
    status: 'active',
  },
  {
    id: 17,
    name: 'Fish and Chips',
    category: 'Entree',
    price: 19.99,
    cost: 7.20,
    stock: 10,
    minStock: 5,
    ingredients: ['White Fish', 'Batter', 'Potatoes', 'Tartar Sauce', 'Mushy Peas'],
    description: 'Classic beer-battered fish with chips',
    status: 'active',
  },
];

export default function EntreeProductsPage() {
  const lowStockCount = entreeProducts.filter(p => p.stock <= p.minStock).length;



  return (
    <div className="space-y-6">
      {/* Category Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Total Entrees</div>
          <div className="text-2xl font-bold text-blue-600">{entreeProducts.length}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Low Stock Items</div>
          <div className="text-2xl font-bold text-red-600">{lowStockCount}</div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {entreeProducts.map((product) => (
          <Boundary
            key={product.id}
            label={product.name}
            className={`bg-white ${
              product.stock <= product.minStock ? 'border-red-300 bg-red-50' : 'border-gray-200'
            }`}
          >
            <div className="space-y-4">
              {/* Product Header */}
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                  <p className="text-sm text-gray-500">{product.category}</p>
                </div>

              </div>

              {/* Description */}
              <p className="text-sm text-gray-600">{product.description}</p>

              {/* Stock Status */}
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">Stock: </span>
                  <span
                    className={`font-bold ${
                      product.stock <= product.minStock ? 'text-red-600' : 'text-green-600'
                    }`}
                  >
                    {product.stock}
                  </span>
                  <span className="text-sm text-gray-500"> (Min: {product.minStock})</span>
                </div>
                {product.stock <= product.minStock && (
                  <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">
                    Low Stock
                  </span>
                )}
              </div>



              {/* Ingredients */}
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Ingredients:</div>
                <div className="flex flex-wrap gap-1">
                  {product.ingredients.map((ingredient, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1">
                  Edit
                </Button>
                <Button className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm">
                  View Recipe
                </Button>
              </div>
            </div>
          </Boundary>
        ))}
      </div>
    </div>
  );
}
