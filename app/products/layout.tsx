'use client';

import { Button } from '#/ui/button';
import clsx from 'clsx';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const productCategories = [
  { name: 'All Products', href: '/products', slug: '' },
  { name: 'Rice', href: '/products/rice', slug: 'rice' },
  { name: 'Noodles', href: '/products/noodles', slug: 'noodles' },
  { name: 'Entree', href: '/products/entree', slug: 'entree' },
  { name: 'Drinks', href: '/products/drinks', slug: 'drinks' },
];

export default function ProductsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Manage your meals and drinks</p>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Product Categories">
          {productCategories.map((category) => {
            const isActive = pathname === category.href;
            return (
              <Link
                key={category.slug || 'all'}
                href={category.href}
                className={clsx(
                  'whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium',
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                )}
              >
                {category.name}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div>{children}</div>
    </div>
  );
}
