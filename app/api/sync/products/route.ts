import { NextResponse } from 'next/server';
import { syncProductsFromSquare } from '#/lib/square-products-service';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function POST() {
  try {
    // Check environment variables first
    if (!process.env.SQUARE_ACCESS_TOKEN) {
      console.error('SQUARE_ACCESS_TOKEN is not configured');
      return NextResponse.json(
        { error: 'Square API not configured - missing access token' },
        { status: 500 }
      );
    }

    if (!process.env.SQUARE_LOCATION_ID) {
      console.error('SQUARE_LOCATION_ID is not configured');
      return NextResponse.json(
        { error: 'Square API not configured - missing location ID' },
        { status: 500 }
      );
    }

    console.log('Starting product sync from Square...');
    
    const result = await syncProductsFromSquare();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        count: result.count,
        timestamp: new Date().toISOString(),
      });
    } else {
      return NextResponse.json(
        { 
          error: result.message,
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Error in product sync API:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { 
        error: 'Failed to sync products from Square',
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to sync products from Square',
    endpoint: '/api/sync/products',
    method: 'POST',
  });
}
