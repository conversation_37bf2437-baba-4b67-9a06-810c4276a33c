import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '#/lib/supabase';

// GET /api/suppliers - Get all suppliers
export async function GET() {
  try {
    console.log('API: Getting all suppliers');
    
    if (!supabaseAdmin) {
      console.error('Supabase admin client not available');
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from('suppliers')
      .select('*')
      .eq('is_active', true)
      .order('name');
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`API: Found ${data?.length || 0} suppliers`);
    return NextResponse.json(data || []);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/suppliers - Create new supplier
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('API: Creating supplier:', body);

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    // Ensure is_active is set to true by default
    const supplierData = {
      ...body,
      is_active: body.is_active !== undefined ? body.is_active : true
    };

    const { data, error } = await supabaseAdmin
      .from('suppliers')
      .insert(supplierData)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('API: Created supplier:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
