import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '#/lib/supabase';

// GET /api/ingredients - Get all ingredients
export async function GET() {
  try {
    console.log('API: Getting all ingredients');
    
    if (!supabaseAdmin) {
      console.error('Supabase admin client not available');
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from('ingredients')
      .select('*')
      .order('name');
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`API: Found ${data?.length || 0} ingredients`);
    return NextResponse.json(data || []);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/ingredients - Create new ingredient
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('API: Creating ingredient:', body);

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    // Convert camelCase to snake_case for database
    const dbData = {
      name: body.name,
      category: body.category,
      unit: body.unit,
      stock: body.stock,
      min_stock: body.minStock || body.min_stock,
      max_stock: body.maxStock || body.max_stock,
      cost_per_unit: body.costPerUnit || body.cost_per_unit,
      supplier: body.supplier,
      location: body.location
    };

    console.log('API: Converted data for database:', dbData);
    console.log('API: costPerUnit value:', body.costPerUnit);
    console.log('API: cost_per_unit value:', dbData.cost_per_unit);

    const { data, error } = await supabaseAdmin
      .from('ingredients')
      .insert(dbData)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('API: Created ingredient:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
