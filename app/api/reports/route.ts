import { NextRequest, NextResponse } from 'next/server';
import { generateSalesReport } from '#/lib/square-service';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Check environment variables first
    if (!process.env.SQUARE_ACCESS_TOKEN) {
      console.error('SQUARE_ACCESS_TOKEN is not configured');
      return NextResponse.json(
        { error: 'Square API not configured - missing access token' },
        { status: 500 }
      );
    }

    if (!process.env.SQUARE_LOCATION_ID) {
      console.error('SQUARE_LOCATION_ID is not configured');
      return NextResponse.json(
        { error: 'Square API not configured - missing location ID' },
        { status: 500 }
      );
    }

    const startDate = request.nextUrl.searchParams.get('startDate');
    const endDate = request.nextUrl.searchParams.get('endDate');

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate and endDate are required' },
        { status: 400 }
      );
    }

    console.log('Fetching real Square POS data...');
    console.log('Environment:', process.env.SQUARE_ENVIRONMENT);
    console.log('Date range:', startDate, 'to', endDate);

    const salesData = await generateSalesReport(startDate, endDate);

    return NextResponse.json(salesData);
  } catch (error) {
    console.error('Error in reports API:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('Error details:', { message: errorMessage, stack: errorStack });

    return NextResponse.json(
      {
        error: 'Failed to fetch sales data',
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
