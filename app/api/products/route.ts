import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '#/lib/supabase';

// GET /api/products - Get all products
export async function GET() {
  try {
    console.log('API: Getting all products');
    
    if (!supabaseAdmin) {
      console.error('Supabase admin client not available');
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from('products')
      .select('*')
      .order('name');
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`API: Found ${data?.length || 0} products`);
    return NextResponse.json(data || []);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/products - Create new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('API: Creating product:', body);

    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    // Convert camelCase to snake_case for database
    const dbData = {
      name: body.name,
      category: body.category,
      price: body.price,
      cost: body.cost,
      description: body.description,
      image_url: body.imageUrl || body.image_url,
      square_catalog_id: body.squareCatalogId || body.square_catalog_id
    };

    console.log('API: Converted data for database:', dbData);

    const { data, error } = await supabaseAdmin
      .from('products')
      .insert(dbData)
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Handle ingredients if provided
    if (body.ingredients && body.ingredients.length > 0) {
      interface IngredientInput {
        ingredient_id: string;
        quantity: number;
        unit?: string;
      }

      const ingredientsToInsert = body.ingredients
        .filter((ing: IngredientInput) => ing.ingredient_id && ing.quantity > 0)
        .map((ing: IngredientInput) => ({
          product_id: data.id,
          ingredient_id: ing.ingredient_id,
          quantity: ing.quantity,
          unit: ing.unit || ''
        }));

      if (ingredientsToInsert.length > 0) {
        const { error: ingredientsError } = await supabaseAdmin
          .from('product_ingredients')
          .insert(ingredientsToInsert);

        if (ingredientsError) {
          console.error('Error inserting ingredients:', ingredientsError);
          // Don't fail the whole operation, just log the error
        }
      }
    }

    console.log('API: Created product:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
