import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '#/lib/supabase';

// GET /api/products/[id] - Get single product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Getting product:', id);
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from('products')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    console.log('API: Updating product:', id, body);
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    // Clean the updates object and convert camelCase to snake_case
    const cleanUpdates = {
      name: body.name,
      category: body.category,
      price: body.price,
      cost: body.cost,
      description: body.description,
      image_url: body.imageUrl || body.image_url,
      square_catalog_id: body.squareCatalogId || body.square_catalog_id,
      updated_at: new Date().toISOString()
    };

    // Remove undefined values
    Object.keys(cleanUpdates).forEach(key => {
      if (cleanUpdates[key as keyof typeof cleanUpdates] === undefined) {
        delete cleanUpdates[key as keyof typeof cleanUpdates];
      }
    });

    console.log('API: Clean updates:', cleanUpdates);

    const { data, error } = await supabaseAdmin
      .from('products')
      .update(cleanUpdates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Handle ingredients if provided
    if (body.ingredients !== undefined) {
      // Delete existing ingredients
      const { error: deleteError } = await supabaseAdmin
        .from('product_ingredients')
        .delete()
        .eq('product_id', id);

      if (deleteError) {
        console.error('Error deleting existing ingredients:', deleteError);
        // Don't fail the whole operation, just log the error
      }

      // Insert new ingredients if any
      if (body.ingredients && body.ingredients.length > 0) {
        interface IngredientInput {
          ingredient_id: string;
          quantity: number;
          unit?: string;
        }

        const ingredientsToInsert = body.ingredients
          .filter((ing: IngredientInput) => ing.ingredient_id && ing.quantity > 0)
          .map((ing: IngredientInput) => ({
            product_id: id,
            ingredient_id: ing.ingredient_id,
            quantity: ing.quantity,
            unit: ing.unit || ''
          }));

        if (ingredientsToInsert.length > 0) {
          const { error: ingredientsError } = await supabaseAdmin
            .from('product_ingredients')
            .insert(ingredientsToInsert);

          if (ingredientsError) {
            console.error('Error inserting ingredients:', ingredientsError);
            // Don't fail the whole operation, just log the error
          }
        }
      }
    }

    console.log('API: Updated product:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/products/[id] - Delete product
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Deleting product:', id);
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { error } = await supabaseAdmin
      .from('products')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('API: Deleted product:', id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
