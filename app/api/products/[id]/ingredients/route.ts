import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '#/lib/supabase';

// GET /api/products/[id]/ingredients - Get ingredients for a product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log('API: Getting ingredients for product:', id);
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    const { data, error } = await supabaseAdmin
      .from('product_ingredients')
      .select(`
        id,
        quantity,
        unit,
        notes,
        ingredient_id,
        ingredients (
          id,
          name,
          unit
        )
      `)
      .eq('product_id', id);
    
    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform the data to match our interface
    interface ProductIngredientRow {
      ingredient_id: string;
      quantity: number;
      unit?: string;
      ingredients?: {
        name: string;
        unit: string;
      };
    }

    const transformedData = data?.map((item: ProductIngredientRow) => ({
      ingredient_id: item.ingredient_id,
      ingredient_name: item.ingredients?.name || '',
      quantity: item.quantity,
      unit: item.unit || item.ingredients?.unit || ''
    })) || [];

    console.log('API: Found ingredients for product:', transformedData);
    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/products/[id]/ingredients - Update ingredients for a product
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { ingredients } = await request.json();
    console.log('API: Updating ingredients for product:', id, ingredients);
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 });
    }

    // Start a transaction by deleting existing ingredients and inserting new ones
    
    // First, delete existing ingredients for this product
    const { error: deleteError } = await supabaseAdmin
      .from('product_ingredients')
      .delete()
      .eq('product_id', id);
    
    if (deleteError) {
      console.error('Error deleting existing ingredients:', deleteError);
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    // Then, insert new ingredients if any
    if (ingredients && ingredients.length > 0) {
      interface IngredientInput {
        ingredient_id: string;
        quantity: number;
        unit?: string;
      }

      const ingredientsToInsert = ingredients
        .filter((ing: IngredientInput) => ing.ingredient_id && ing.quantity > 0)
        .map((ing: IngredientInput) => ({
          product_id: id,
          ingredient_id: ing.ingredient_id,
          quantity: ing.quantity,
          unit: ing.unit || ''
        }));

      if (ingredientsToInsert.length > 0) {
        const { error: insertError } = await supabaseAdmin
          .from('product_ingredients')
          .insert(ingredientsToInsert);
        
        if (insertError) {
          console.error('Error inserting new ingredients:', insertError);
          return NextResponse.json({ error: insertError.message }, { status: 500 });
        }
      }
    }

    console.log('API: Successfully updated ingredients for product:', id);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
