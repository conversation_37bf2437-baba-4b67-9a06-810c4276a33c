'use client';

import { useState } from 'react';

export default function ApiTestPage() {
  const [response, setResponse] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testEndpoint = async (method: string, endpoint: string, body?: Record<string, unknown>) => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body) {
        options.body = JSON.stringify(body);
      }

      const res = await fetch(endpoint, options);
      const data = await res.json();

      setResponse({
        status: res.status,
        statusText: res.statusText,
        data: data
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">API Testing</h1>
            <p className="mt-2 text-gray-600">
              Test the Inventory Management API endpoints
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Cases</h2>
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Get All Ingredients</h3>
                <p className="text-sm text-gray-600 mb-2">Fetch all ingredients from the database</p>
                <code className="text-xs bg-gray-100 px-2 py-1 rounded block mb-3">
                  GET /api/ingredients
                </code>
                <button
                  onClick={() => testEndpoint('GET', '/api/ingredients')}
                  disabled={loading}
                  className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test'}
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Get All Suppliers</h3>
                <p className="text-sm text-gray-600 mb-2">Fetch all active suppliers</p>
                <code className="text-xs bg-gray-100 px-2 py-1 rounded block mb-3">
                  GET /api/suppliers
                </code>
                <button
                  onClick={() => testEndpoint('GET', '/api/suppliers')}
                  disabled={loading}
                  className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test'}
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Create Test Ingredient</h3>
                <p className="text-sm text-gray-600 mb-2">Create a new test ingredient with snake_case fields</p>
                <code className="text-xs bg-gray-100 px-2 py-1 rounded block mb-3">
                  POST /api/ingredients
                </code>
                <button
                  onClick={() => testEndpoint('POST', '/api/ingredients', {
                    name: 'Test Ingredient API',
                    category: 'Dry',
                    unit: 'lbs',
                    stock: 10,
                    min_stock: 5,
                    max_stock: 20,
                    cost_per_unit: 1.99,
                    supplier: 'Test Supplier',
                    location: 'Test Storage'
                  })}
                  disabled={loading}
                  className="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test'}
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Response</h2>
            
            {loading && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Testing API endpoint...</p>
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-red-800 mb-2">Error</h3>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            {response && (
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <span className="px-3 py-1 text-sm font-medium rounded bg-green-100 text-green-800">
                    {response.status} {response.statusText}
                  </span>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Response Data</h3>
                  <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto max-h-96">
                    {JSON.stringify(response.data, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {!loading && !error && !response && (
              <div className="text-center py-8 text-gray-500">
                <p>Select a test case to see the response</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
