'use client';

import { useEffect, useState } from 'react';

export default function ApiDocsPage() {
  const [swaggerSpec, setSwaggerSpec] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    // Fetch the OpenAPI spec
    fetch('/api/docs')
      .then(res => res.json())
      .then(spec => setSwaggerSpec(spec))
      .catch(err => console.error('Error loading API spec:', err));
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">API Documentation</h1>
            <p className="mt-2 text-gray-600">
              Interactive documentation for the Inventory Management API
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Quick Links</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h3 className="font-medium text-gray-900">Base URL</h3>
                  <code className="text-sm text-blue-600">http://localhost:3000/api</code>
                </div>
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h3 className="font-medium text-gray-900">Ingredients</h3>
                  <p className="text-sm text-gray-600">Manage inventory ingredients</p>
                </div>
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h3 className="font-medium text-gray-900">Suppliers</h3>
                  <p className="text-sm text-gray-600">Manage supplier information</p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Available Endpoints</h2>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">GET</span>
                  <code className="text-sm">/api/ingredients</code>
                  <span className="text-sm text-gray-600">Get all ingredients</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">POST</span>
                  <code className="text-sm">/api/ingredients</code>
                  <span className="text-sm text-gray-600">Create new ingredient</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">GET</span>
                  <code className="text-sm">/api/ingredients/{'{id}'}</code>
                  <span className="text-sm text-gray-600">Get ingredient by ID</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">PUT</span>
                  <code className="text-sm">/api/ingredients/{'{id}'}</code>
                  <span className="text-sm text-gray-600">Update ingredient</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">DELETE</span>
                  <code className="text-sm">/api/ingredients/{'{id}'}</code>
                  <span className="text-sm text-gray-600">Delete ingredient</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">GET</span>
                  <code className="text-sm">/api/suppliers</code>
                  <span className="text-sm text-gray-600">Get all suppliers</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">POST</span>
                  <code className="text-sm">/api/suppliers</code>
                  <span className="text-sm text-gray-600">Create new supplier</span>
                </div>
              </div>
            </div>

            {!swaggerSpec && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading API documentation...</p>
              </div>
            )}

            {swaggerSpec && (
              <div className="mt-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">OpenAPI Specification</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Interactive Documentation</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      For the best experience, copy the JSON below and paste it into{' '}
                      <a
                        href="https://editor.swagger.io/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        Swagger Editor
                      </a>
                      {' '}or{' '}
                      <a
                        href="https://petstore.swagger.io/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        Swagger UI
                      </a>
                    </p>
                    <button
                      onClick={() => navigator.clipboard.writeText(JSON.stringify(swaggerSpec, null, 2))}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                    >
                      📋 Copy JSON to Clipboard
                    </button>
                  </div>
                  <details className="mt-4">
                    <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                      View Raw OpenAPI Specification
                    </summary>
                    <pre className="mt-3 bg-white p-4 rounded border text-xs overflow-auto max-h-96">
                      {JSON.stringify(swaggerSpec, null, 2)}
                    </pre>
                  </details>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
