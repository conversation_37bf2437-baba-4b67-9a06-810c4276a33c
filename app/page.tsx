import { Boundary } from '#/ui/boundary';
import { formatRelativeTime } from '#/lib/inventory-utils';
import { dashboardStats, lowStockAlerts, recentActivity } from '#/lib/dummy-data';
// import { DashboardWrapper } from '#/components/dashboard/dashboard-wrapper';
import Link from 'next/link';

export default function Dashboard() {
  // Use a static timestamp for SSR compatibility
  const lastUpdated = "January 13, 2024 at 2:30 PM";

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Inventory Dashboard</h1>
        <div className="text-sm text-gray-500">
          Last updated: {lastUpdated}
        </div>
      </div>

      {/* Quick Navigation */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Link
          href="/products"
          className="group rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                Products
              </h3>
              <p className="text-sm text-gray-500">Manage meals and drinks</p>
            </div>
            <div className="text-2xl font-bold text-blue-600">{dashboardStats.totalProducts}</div>
          </div>
        </Link>

        <Link
          href="/ingredients"
          className="group rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600">
                Ingredients
              </h3>
              <p className="text-sm text-gray-500">Track raw materials</p>
            </div>
            <div className="text-2xl font-bold text-green-600">{dashboardStats.totalIngredients}</div>
          </div>
        </Link>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Low Stock Products</div>
          <div className="text-2xl font-bold text-red-600">{dashboardStats.lowStockProducts}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Low Stock Ingredients</div>
          <div className="text-2xl font-bold text-red-600">{dashboardStats.lowStockIngredients}</div>
        </div>
      </div>

      {/* Low Stock Alerts */}
      <Boundary label="Low Stock Alerts" className="bg-red-50 border-red-200">
        <div className="space-y-3">
          {lowStockAlerts.map((alert) => (
            <div key={alert.id} className="flex items-center justify-between rounded-lg bg-white p-4 border border-red-200">
              <div>
                <div className="font-medium text-gray-900">{alert.name}</div>
                <div className="text-sm text-gray-500 capitalize">{alert.type}</div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-red-600">
                  {alert.stock} {alert.unit}
                </div>
                <div className="text-sm text-gray-500">
                  Min: {alert.minStock} {alert.unit}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                  alert.severity === 'critical'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {alert.severity === 'critical' ? 'Critical' : 'Low Stock'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </Boundary>

      {/* Recent Activity */}
      <Boundary label="Recent Activity">
        <div className="space-y-3">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
              <div>
                <div className="font-medium text-gray-900">{activity.action}</div>
                <div className="text-sm text-gray-500">{activity.item}</div>
                {activity.details && (
                  <div className="text-xs text-gray-400 mt-1">{activity.details}</div>
                )}
              </div>
              <div className="text-sm text-gray-400">{formatRelativeTime(activity.timestamp)}</div>
            </div>
          ))}
        </div>
      </Boundary>
    </div>
  );
}
