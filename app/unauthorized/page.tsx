'use client';

import Link from 'next/link';
import { useAuth } from '#/lib/auth-context';
import { Button } from '#/ui/button';

export default function UnauthorizedPage() {
  const { user, profile, signOut } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <span className="text-4xl">🚫</span>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            You don&apos;t have permission to access this resource.
          </p>
        </div>

        {user && profile && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Current User
              </h3>
              <p className="text-sm text-gray-600 mb-1">
                <span className="font-medium">Email:</span> {user.email}
              </p>
              <p className="text-sm text-gray-600 mb-4">
                <span className="font-medium">Role:</span>{' '}
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  profile.role === 'admin' 
                    ? 'bg-blue-100 text-blue-800'
                    : profile.role === 'staff'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {profile.role}
                </span>
              </p>
            </div>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Need different access?
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Contact your administrator to request the appropriate role for your needs.</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-4">
          <Link href="/" className="flex-1">
            <Button className="w-full bg-gray-600 hover:bg-gray-700 text-white">
              Go to Dashboard
            </Button>
          </Link>
          <Button 
            onClick={signOut}
            kind="error"
            className="flex-1"
          >
            Sign Out
          </Button>
        </div>

        <div className="text-center text-xs text-gray-500">
          <div className="font-medium mb-2">Role Permissions:</div>
          <div className="space-y-1">
            <div><span className="font-semibold text-blue-600">Admin:</span> Full system access, reports, sync, user management</div>
            <div><span className="font-semibold text-green-600">Staff:</span> Inventory management, restocking, no reports</div>
            <div><span className="font-semibold text-gray-600">Viewer:</span> Read-only access to products and ingredients</div>
          </div>
        </div>
      </div>
    </div>
  );
}
