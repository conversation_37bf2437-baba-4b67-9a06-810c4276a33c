'use client';

import clsx from 'clsx';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const ingredientCategories = [
  { name: 'All Ingredients', href: '/ingredients', slug: '' },
  { name: 'Meat', href: '/ingredients/meat', slug: 'meat' },
  { name: 'Dry', href: '/ingredients/dry', slug: 'dry' },
  { name: 'Liquid', href: '/ingredients/liquid', slug: 'liquid' },
  { name: 'Bag', href: '/ingredients/bag', slug: 'bag' },
];

export default function IngredientsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="space-y-6">
      {/* Category Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8" aria-label="Ingredient Categories">
          {ingredientCategories.map((category) => {
            const isActive = pathname === category.href;
            return (
              <Link
                key={category.slug || 'all'}
                href={category.href}
                className={clsx(
                  'whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium',
                  isActive
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                )}
              >
                {category.name}
              </Link>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div>{children}</div>
    </div>
  );
}
