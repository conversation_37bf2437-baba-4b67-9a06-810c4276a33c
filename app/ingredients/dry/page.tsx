import { Boundary } from '#/ui/boundary';
import { But<PERSON> } from '#/ui/button';
import { getDaysUntilExpiry, getStockStatus } from '#/lib/inventory-utils';

// Mock data for dry ingredients
const dryIngredients = [
  {
    id: 7,
    name: 'Jasmine Rice',
    category: 'Dry',
    unit: 'lbs',
    stock: 50,
    minStock: 20,
    maxStock: 100,
    costPerUnit: 2.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-05',
    expiryDate: '2024-12-31',
    location: 'Dry Storage A',
  },
  {
    id: 8,
    name: 'Rice Noodles',
    category: 'Dry',
    unit: 'lbs',
    stock: 15,
    minStock: 10,
    maxStock: 50,
    costPerUnit: 3.49,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-08',
    expiryDate: '2024-08-15',
    location: 'Dry Storage B',
  },
  {
    id: 9,
    name: 'All-Purpose Flour',
    category: 'Dry',
    unit: 'lbs',
    stock: 25,
    minStock: 15,
    maxStock: 60,
    costPerUnit: 1.99,
    supplier: 'Baking Supplies Inc.',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-06-30',
    location: 'Dry Storage C',
  },
  {
    id: 10,
    name: 'Coffee Beans',
    category: 'Dry',
    unit: 'lbs',
    stock: 8,
    minStock: 5,
    maxStock: 25,
    costPerUnit: 12.99,
    supplier: 'Premium Coffee Co.',
    lastRestocked: '2024-01-05',
    expiryDate: '2024-03-05',
    location: 'Dry Storage D',
  },
  {
    id: 11,
    name: 'Sugar',
    category: 'Dry',
    unit: 'lbs',
    stock: 30,
    minStock: 20,
    maxStock: 80,
    costPerUnit: 1.49,
    supplier: 'Sweet Supply Co.',
    lastRestocked: '2024-01-12',
    expiryDate: '2025-01-12',
    location: 'Dry Storage E',
  },
  {
    id: 12,
    name: 'Black Pepper',
    category: 'Dry',
    unit: 'oz',
    stock: 12,
    minStock: 8,
    maxStock: 30,
    costPerUnit: 0.99,
    supplier: 'Spice World',
    lastRestocked: '2024-01-07',
    expiryDate: '2024-07-07',
    location: 'Spice Rack A',
  },
];

export default function DryIngredientsPage() {
  const lowStockCount = dryIngredients.filter(i => i.stock <= i.minStock).length;
  const expiringSoonCount = dryIngredients.filter(i => getDaysUntilExpiry(i.expiryDate) <= 30).length;
  const totalValue = dryIngredients.reduce((sum, i) => sum + (i.stock * i.costPerUnit), 0);

  return (
    <div className="space-y-6">
      {/* Category Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Total Dry Items</div>
          <div className="text-2xl font-bold text-gray-900">{dryIngredients.length}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Low Stock Items</div>
          <div className="text-2xl font-bold text-red-600">{lowStockCount}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Expiring Soon (30 days)</div>
          <div className="text-2xl font-bold text-yellow-600">{expiringSoonCount}</div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="text-sm font-medium text-gray-500">Total Value</div>
          <div className="text-2xl font-bold text-green-600">${totalValue.toFixed(2)}</div>
        </div>
      </div>

      {/* Ingredients Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {dryIngredients.map((ingredient) => {
          const stockStatus = getStockStatus(ingredient.stock, ingredient.minStock, ingredient.maxStock);
          const daysUntilExpiry = getDaysUntilExpiry(ingredient.expiryDate);
          const isExpiringSoon = daysUntilExpiry <= 30; // Dry goods have longer shelf life

          return (
            <Boundary
              key={ingredient.id}
              label={ingredient.name}
              className={`bg-white ${
                stockStatus === 'low' || isExpiringSoon
                  ? 'border-red-300 bg-red-50'
                  : 'border-gray-200'
              }`}
            >
              <div className="space-y-4">
                {/* Ingredient Header */}
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{ingredient.name}</h3>
                    <p className="text-sm font-medium text-gray-600">{ingredient.category}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      ${ingredient.costPerUnit}/{ingredient.unit}
                    </div>
                  </div>
                </div>

                {/* Stock Level */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">Stock Level</span>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        stockStatus === 'low'
                          ? 'bg-red-100 text-red-800'
                          : stockStatus === 'high'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {stockStatus === 'low' ? 'Low Stock' : 
                       stockStatus === 'high' ? 'Well Stocked' : 'Normal'}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        stockStatus === 'low'
                          ? 'bg-red-500'
                          : stockStatus === 'high'
                          ? 'bg-green-500'
                          : 'bg-blue-500'
                      }`}
                      style={{
                        width: `${Math.min((ingredient.stock / ingredient.maxStock) * 100, 100)}%`,
                      }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Current: {ingredient.stock} {ingredient.unit}</span>
                    <span>Max: {ingredient.maxStock} {ingredient.unit}</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    Min: {ingredient.minStock} {ingredient.unit}
                  </div>
                </div>

                {/* Expiry Warning */}
                {isExpiringSoon && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-600 font-medium text-sm">⚠️ Check Expiry</span>
                    </div>
                    <div className="text-sm text-yellow-700">
                      {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : 'Expired'}
                    </div>
                  </div>
                )}

                {/* Details */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Supplier:</span>
                    <span className="text-gray-900">{ingredient.supplier}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Location:</span>
                    <span className="text-gray-900">{ingredient.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Last Restocked:</span>
                    <span className="text-gray-900">{ingredient.lastRestocked}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Expires:</span>
                    <span className={`${isExpiringSoon ? 'text-yellow-600 font-medium' : 'text-gray-900'}`}>
                      {ingredient.expiryDate}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium">
                    Edit
                  </Button>
                  <Button className="flex-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium">
                    Restock
                  </Button>
                </div>
              </div>
            </Boundary>
          );
        })}
      </div>
    </div>
  );
}
