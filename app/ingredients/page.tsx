'use client';

import { useState, useEffect } from 'react';
import { Boundary } from '#/ui/boundary';
import { getStockStatus } from '#/lib/inventory-utils';
import { ingredientsService, Ingredient } from '#/lib/ingredients-service';
import { allIngredients } from '#/lib/dummy-data';
import { AddIngredientModal } from '#/components/ingredients/add-ingredient-modal';
import { EditIngredientModal } from '#/components/ingredients/edit-ingredient-modal';
import { RestockModal } from '#/components/ingredients/restock-modal';

// Import types
interface NewIngredient {
  name: string;
  category: string;
  unit: string;
  stock: number;
  minStock: number;
  maxStock: number;
  costPerUnit: number;
  supplier: string;
  location: string;
  expiryDate: string;
}

interface EditIngredient {
  id: string;
  name: string;
  category: string;
  unit: string;
  stock: number;
  min_stock: number;
  max_stock: number;
  cost_per_unit: number;
  supplier: string;
  location: string;
  expiryDate: string;
}

interface RestockData {
  ingredientId: string;
  ingredientName: string;
  currentStock: number;
  addAmount: number;
  newStock: number;
  unit: string;
  costPerUnit: number;
  totalCost: number;
  supplier: string;
  notes: string;
  restockDate: string;
}
import { SuppliersModal } from '#/components/suppliers/suppliers-modal';
// import { IngredientsWrapper } from '#/components/ingredients/ingredients-wrapper';



export default function IngredientsPage() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isRestockModalOpen, setIsRestockModalOpen] = useState(false);
  const [isSuppliersModalOpen, setIsSuppliersModalOpen] = useState(false);
  const [selectedIngredient, setSelectedIngredient] = useState<Ingredient | null>(null);
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [filteredIngredients, setFilteredIngredients] = useState<Ingredient[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);

  // Load ingredients on component mount
  useEffect(() => {
    setMounted(true);
    loadIngredients();
  }, []);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      console.log('Loading ingredients from database...');
      const data = await ingredientsService.getAllIngredients();
      console.log('Loaded ingredients:', data);

      if (data && data.length > 0) {
        console.log('Using database data');
        setIngredients(data);
        setFilteredIngredients(data);
      } else {
        console.log('No data from database, using dummy data as fallback');
        // Convert dummy data to match Ingredient interface
        const dummyIngredients = allIngredients.map((item, index) => ({
          id: `demo-ingredient-${index + 1}`,
          name: item.name,
          category: item.category,
          unit: item.unit,
          stock: item.stock,
          min_stock: item.minStock,
          max_stock: item.maxStock,
          cost_per_unit: item.costPerUnit,
          supplier: item.supplier,
          location: item.location,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));
        setIngredients(dummyIngredients);
        setFilteredIngredients(dummyIngredients);
      }
    } catch (error) {
      console.error('Error loading ingredients:', error);
      console.log('Using dummy data as fallback due to error');
      // Use dummy data as fallback on error
      const dummyIngredients = allIngredients.map((item, index) => ({
        id: `demo-ingredient-${index + 1}`,
        name: item.name,
        category: item.category,
        unit: item.unit,
        stock: item.stock,
        min_stock: item.minStock,
        max_stock: item.maxStock,
        cost_per_unit: item.costPerUnit,
        supplier: item.supplier,
        location: item.location,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      setIngredients(dummyIngredients);
      setFilteredIngredients(dummyIngredients);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term.trim() === '') {
      setFilteredIngredients(ingredients);
    } else {
      const filtered = ingredients.filter(ingredient =>
        ingredient.name.toLowerCase().includes(term.toLowerCase()) ||
        ingredient.category.toLowerCase().includes(term.toLowerCase()) ||
        ingredient.location?.toLowerCase().includes(term.toLowerCase()) ||
        ingredient.supplier?.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredIngredients(filtered);
    }
  };

  const handleAddIngredient = async (newIngredient: NewIngredient) => {
    try {
      await ingredientsService.createIngredient(newIngredient);
      setNotification({ type: 'success', message: `Successfully added ${newIngredient.name}!` });
      loadIngredients(); // Refresh the list

      // Clear notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error('Error adding ingredient:', error);
      setNotification({ type: 'error', message: 'Error adding ingredient. Please try again.' });

      // Clear notification after 5 seconds for errors
      setTimeout(() => setNotification(null), 5000);
    }
  };

  const handleEditIngredient = async (updatedIngredient: EditIngredient) => {
    console.log('handleEditIngredient called with:', updatedIngredient);

    try {
      const result = await ingredientsService.updateIngredient(updatedIngredient.id, updatedIngredient);
      console.log('Update result:', result);

      // Success - refresh the ingredients list from database
      setNotification({ type: 'success', message: `Successfully updated ${updatedIngredient.name}!` });
      await loadIngredients(); // Refresh the list from database

      // Close the edit modal
      setIsEditModalOpen(false);
      setSelectedIngredient(null);

      // Clear notification after 3 seconds
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error('Error updating ingredient:', error);
      console.error('Error type:', typeof error);
      console.error('Error constructor:', error?.constructor?.name);

      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error message:', errorMessage);

      setNotification({ type: 'error', message: `Error updating ingredient: ${errorMessage || 'Unknown error'}` });

      // Clear notification after 5 seconds for errors
      setTimeout(() => setNotification(null), 5000);
    }
  };

  const handleRestock = async (restockData: RestockData) => {
    try {
      await ingredientsService.restockIngredient(restockData);
      alert(`Successfully restocked ${restockData.ingredientName}!\n\nAdded: ${restockData.addAmount} ${restockData.unit}\nNew Stock: ${restockData.newStock} ${restockData.unit}\nTotal Cost: $${restockData.totalCost.toFixed(2)}\n\nPrice history has been recorded for future analysis.`);
      loadIngredients(); // Refresh the list
    } catch (error) {
      console.error('Error restocking ingredient:', error);
      alert('Error restocking ingredient. Please try again.');
    }
  };

  const openEditModal = (ingredient: Ingredient) => {
    setSelectedIngredient(ingredient);
    setIsEditModalOpen(true);
  };

  const openRestockModal = (ingredient: Ingredient) => {
    setSelectedIngredient(ingredient);
    setIsRestockModalOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
          notification.type === 'success'
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          <div className="flex items-center">
            <span className="mr-2">
              {notification.type === 'success' ? '✅' : '❌'}
            </span>
            {notification.message}
            <button
              onClick={() => setNotification(null)}
              className="ml-3 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Header with Search and Add Button */}
      <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:items-center md:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Ingredients</h1>
          <p className="text-gray-600">Manage your ingredient inventory and stock levels</p>
        </div>

        {/* Search Bar and Buttons */}
        <div className="flex flex-col space-y-3 md:flex-row md:space-y-0 md:space-x-3">
          <div className="relative">
            <input
              type="text"
              placeholder="Search ingredients..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <div className="flex gap-2">
            <button
              className="rounded-md px-3 py-1 text-sm font-semibold transition-colors bg-gray-600 hover:bg-gray-700 text-white"
              onClick={() => setIsSuppliersModalOpen(true)}
            >
              Manage Suppliers
            </button>
            <button
              className="rounded-md px-3 py-1 text-sm font-semibold transition-colors bg-blue-600 hover:bg-blue-700 text-white"
              onClick={() => setIsAddModalOpen(true)}
            >
              + Add Ingredient
            </button>
          </div>
        </div>
      </div>

      {/* Add Ingredient Modal */}
      <AddIngredientModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSubmit={handleAddIngredient}
      />

      {/* Edit Ingredient Modal */}
      <EditIngredientModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleEditIngredient}
        ingredient={selectedIngredient}
      />

      {/* Restock Modal */}
      <RestockModal
        isOpen={isRestockModalOpen}
        onClose={() => setIsRestockModalOpen(false)}
        onSubmit={handleRestock}
        ingredient={selectedIngredient}
      />

      {/* Suppliers Modal */}
      <SuppliersModal
        isOpen={isSuppliersModalOpen}
        onClose={() => setIsSuppliersModalOpen(false)}
      />

      {/* Search Results Info */}
      {searchTerm && (
        <div className="text-sm text-gray-600 mb-4">
          {filteredIngredients.length === 0
            ? `No ingredients found for "${searchTerm}"`
            : `Found ${filteredIngredients.length} ingredient${filteredIngredients.length === 1 ? '' : 's'} for "${searchTerm}"`
          }
        </div>
      )}

      {/* Ingredients Grid */}
      {!mounted || loading ? (
        <div className="text-center py-8">
          <div className="text-gray-500">Loading ingredients...</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredIngredients.map((ingredient) => {
            const stockStatus = getStockStatus(ingredient.stock, ingredient.min_stock, ingredient.max_stock);
            // For now, we'll skip expiry date logic since it's not in our Supabase schema yet
            const isExpiringSoon = false;

          return (
            <Boundary
              key={ingredient.id}
              label={ingredient.name}
              className={`bg-white ${
                stockStatus === 'low' || isExpiringSoon
                  ? 'border-red-300 bg-red-50'
                  : 'border-gray-200'
              }`}
            >
              <div className="space-y-4">
                {/* Ingredient Header */}
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{ingredient.name}</h3>
                    <p className="text-sm font-medium text-gray-600">{ingredient.category}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      ${ingredient.cost_per_unit}/{ingredient.unit}
                    </div>
                  </div>
                </div>

                {/* Stock Level */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-800">Stock Level</span>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        stockStatus === 'low'
                          ? 'bg-red-100 text-red-800'
                          : stockStatus === 'high'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {stockStatus === 'low' ? 'Low Stock' :
                       stockStatus === 'high' ? 'Well Stocked' : 'Normal'}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        stockStatus === 'low'
                          ? 'bg-red-500'
                          : stockStatus === 'high'
                          ? 'bg-green-500'
                          : 'bg-blue-500'
                      }`}
                      style={{
                        width: `${Math.min((ingredient.stock / (ingredient.max_stock || ingredient.stock)) * 100, 100)}%`,
                      }}
                    ></div>
                  </div>

                  <div className="flex justify-between text-sm font-medium text-gray-700">
                    <span>Current: {ingredient.stock} {ingredient.unit}</span>
                    <span>Max: {ingredient.max_stock || 'N/A'} {ingredient.unit}</span>
                  </div>
                  <div className="text-sm font-medium text-gray-600">
                    Min: {ingredient.min_stock} {ingredient.unit}
                  </div>
                </div>

                {/* Expiry Warning - Disabled for now since we don't have expiry dates in Supabase */}
                {false && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-600 font-medium text-sm">⚠️ Expires Soon</span>
                    </div>
                    <div className="text-sm text-yellow-700">
                      Expiry tracking coming soon
                    </div>
                  </div>
                )}

                {/* Details */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Supplier:</span>
                    <span className="text-gray-900">{ingredient.supplier}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Location:</span>
                    <span className="text-gray-900">{ingredient.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Supplier:</span>
                    <span className="text-gray-900">{ingredient.supplier || 'Not specified'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Location:</span>
                    <span className="text-gray-900">{ingredient.location || 'Not specified'}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <button
                    className="flex-1 rounded-md px-3 py-1 text-sm font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white"
                    onClick={() => openEditModal(ingredient)}
                  >
                    Edit
                  </button>
                  <button
                    className="flex-1 rounded-md px-3 py-1 text-sm font-medium transition-colors bg-amber-600 hover:bg-amber-700 text-white"
                    onClick={() => openRestockModal(ingredient)}
                  >
                    Restock
                  </button>
                </div>
              </div>
            </Boundary>
          );
          })}
        </div>
      )}

      {/* Summary Stats */}
      <Boundary label="Ingredients Summary">
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{ingredients.length}</div>
            <div className="text-sm text-gray-500">Total Ingredients</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {ingredients.filter((i: Ingredient) => i.stock <= i.min_stock).length}
            </div>
            <div className="text-sm text-gray-500">Low Stock Items</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              0
            </div>
            <div className="text-sm text-gray-500">Expiring Soon</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
${ingredients.reduce((sum: number, i: Ingredient) => sum + (i.stock * i.cost_per_unit), 0).toFixed(2)}
            </div>
            <div className="text-sm text-gray-500">Total Inventory Value</div>
          </div>
        </div>
      </Boundary>
    </div>
  );
}
