# 🟦 Square POS Integration Guide

## Overview
Your inventory management system now includes full Square POS integration for real-time product sync and sales reporting.

## 🚀 Quick Start

### 1. Check Current Setup
```bash
npm run setup:square
```

### 2. Start App with Square Enabled
```bash
npm run dev:square
```

### 3. Alternative Start (if setup is complete)
```bash
npm run dev
```

## 🔧 Environment Configuration

### Required Variables (.env.local)
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Square POS Integration
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_LOCATION_ID=your_square_location_id
SQUARE_ENVIRONMENT=sandbox
SQUARE_APPLICATION_ID=your_square_app_id
```

## 🟦 Square Developer Setup

### 1. Create Square Developer Account
- Visit [developer.squareup.com](https://developer.squareup.com)
- Sign up or log in with your Square account

### 2. Create Application
- Go to "My Applications"
- Click "Create App"
- Choose "Build on Square APIs"
- Enter app name and description

### 3. Get Credentials
- **Access Token**: Found in app dashboard under "Credentials"
- **Application ID**: Found in app dashboard under "Credentials"
- **Location ID**: Found under "Locations" tab

### 4. Set Environment
- **Sandbox**: For testing (use sandbox credentials)
- **Production**: For live data (use production credentials)

## 🎯 Features Available

### ✅ Product Management
- **Sync from Square**: Import products from Square catalog
- **Auto-categorization**: Products automatically sorted by type
- **Real-time updates**: Changes sync between systems
- **Image support**: Product images from Square

### ✅ Sales Reporting
- **Daily/Weekly/Monthly reports**: Comprehensive sales analytics
- **Top products**: Best-selling items analysis
- **Revenue tracking**: Total sales and trends
- **Order details**: Individual transaction data

### ✅ Inventory Integration
- **Stock levels**: Manual inventory tracking
- **Low stock alerts**: Automatic notifications
- **Cost tracking**: Ingredient cost management
- **Recipe management**: Product composition tracking

## 🔍 Testing the Integration

### 1. Product Sync Test
1. Go to `/products` page
2. Click "🔄 Sync from Square" button
3. Check console for sync progress
4. Verify products appear in the list

### 2. Reports Test
1. Go to `/reports` page
2. Select date range
3. Click "Generate Report"
4. Verify Square sales data appears

### 3. Health Check
1. Visit `/api/health`
2. Check all environment variables are "true"
3. Verify Square API connectivity

## 🚨 Troubleshooting

### Common Issues

#### 1. "Square API not configured"
- **Cause**: Missing environment variables
- **Fix**: Add SQUARE_ACCESS_TOKEN and SQUARE_LOCATION_ID to .env.local

#### 2. "Authentication failed"
- **Cause**: Invalid access token
- **Fix**: Regenerate token in Square Developer Dashboard

#### 3. "Location not found"
- **Cause**: Wrong location ID
- **Fix**: Check location ID in Square Dashboard → Locations

#### 4. "Permission denied"
- **Cause**: Insufficient app permissions
- **Fix**: Enable required permissions in Square app settings

### Debug Steps
1. **Check environment**: `npm run setup:square`
2. **Check API health**: Visit `/api/health`
3. **Check browser console**: Look for error messages
4. **Check server logs**: Monitor terminal output

## 📊 API Endpoints

### Product Sync
- **POST** `/api/sync/products` - Sync products from Square
- **Response**: `{ success: boolean, count: number, message: string }`

### Sales Reports
- **GET** `/api/reports?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD`
- **Response**: Sales data with metrics and charts

### Health Check
- **GET** `/api/health`
- **Response**: System status and environment check

## 🔐 Security Notes

### Production Deployment
- Use **production** Square credentials for live data
- Keep access tokens secure (never commit to git)
- Use environment variables in deployment platform
- Enable HTTPS for all API calls

### Permissions Required
- **ITEMS_READ**: Read catalog items
- **ORDERS_READ**: Read order data
- **PAYMENTS_READ**: Read payment information

## 🎉 Success Indicators

When everything is working correctly, you should see:

### ✅ In Browser Console:
```
Square API configuration:
- Base URL: https://connect.squareupsandbox.com
- SQUARE_ACCESS_TOKEN: Present
- SQUARE_ENVIRONMENT: sandbox
- SQUARE_LOCATION_ID: Present
```

### ✅ In Products Page:
- Green "🔄 Sync from Square" button (not disabled)
- Products appear after sync
- Product images and descriptions from Square

### ✅ In Reports Page:
- Real sales data from Square
- Charts and metrics display
- Date range filtering works

Your Square integration is now ready! 🎉
