#!/usr/bin/env node

/**
 * Database Setup Script for New Supabase Database
 * This script will create all necessary tables for the inventory management system
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🚀 Setting up database...');
console.log('📍 Supabase URL:', supabaseUrl);
console.log('🔑 Service Key:', supabaseServiceKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const setupSQL = `
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create cached_orders table (required for Square integration)
CREATE TABLE IF NOT EXISTS cached_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    square_order_id TEXT UNIQUE NOT NULL,
    location_id TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    state TEXT NOT NULL,
    total_money BIGINT DEFAULT 0,
    total_tax_money BIGINT DEFAULT 0,
    total_discount_money BIGINT DEFAULT 0,
    line_items JSONB DEFAULT '[]'::jsonb,
    fulfillments JSONB DEFAULT '[]'::jsonb,
    tenders JSONB DEFAULT '[]'::jsonb,
    cached_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_state CHECK (state IN ('OPEN', 'COMPLETED', 'CANCELED'))
);

-- Create cached_products table
CREATE TABLE IF NOT EXISTS cached_products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    square_catalog_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    category TEXT,
    price BIGINT DEFAULT 0,
    description TEXT,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    cached_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create sales_metrics table
CREATE TABLE IF NOT EXISTS sales_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    location_id TEXT NOT NULL,
    total_sales DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_orders INTEGER NOT NULL DEFAULT 0,
    average_order_value DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_tax DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_tips DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, location_id)
);

-- Create sync_logs table
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type TEXT NOT NULL,
    status TEXT NOT NULL,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    records_processed INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_sync_type CHECK (sync_type IN ('orders', 'products', 'metrics')),
    CONSTRAINT valid_status CHECK (status IN ('started', 'completed', 'failed'))
);

-- Create products table for inventory
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    stock INTEGER NOT NULL DEFAULT 0,
    min_stock INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    square_catalog_id TEXT UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_product_status CHECK (status IN ('active', 'inactive', 'discontinued'))
);

-- Create ingredients table
CREATE TABLE IF NOT EXISTS ingredients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    unit TEXT NOT NULL,
    stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    min_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    max_stock DECIMAL(10,2) DEFAULT NULL,
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
    supplier TEXT,
    last_restocked DATE,
    expiry_date DATE,
    location TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_cached_orders_square_id ON cached_orders(square_order_id);
CREATE INDEX IF NOT EXISTS idx_cached_orders_location ON cached_orders(location_id);
CREATE INDEX IF NOT EXISTS idx_cached_orders_created ON cached_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_cached_products_square_id ON cached_products(square_catalog_id);
CREATE INDEX IF NOT EXISTS idx_sales_metrics_date ON sales_metrics(date);
CREATE INDEX IF NOT EXISTS idx_sales_metrics_location ON sales_metrics(location_id);
`;

const sampleDataSQL = `
-- Insert sample products
INSERT INTO products (name, category, price, cost, stock, min_stock, description, status) VALUES
('Chicken Fried Rice', 'Rice', 13.99, 4.80, 18, 5, 'Classic fried rice with tender chicken and fresh vegetables', 'active'),
('Vegetable Fried Rice', 'Rice', 11.99, 3.50, 22, 5, 'Healthy vegetarian fried rice with seasonal vegetables', 'active'),
('Pad Thai', 'Noodles', 14.99, 5.20, 15, 5, 'Traditional Thai stir-fried noodles with tamarind sauce', 'active'),
('Grilled Salmon', 'Entree', 22.99, 12.50, 8, 3, 'Fresh Atlantic salmon grilled to perfection', 'active'),
('Iced Coffee', 'Drinks', 3.99, 1.20, 50, 20, 'Freshly brewed coffee served over ice', 'active')
ON CONFLICT (square_catalog_id) DO NOTHING;

-- Insert sample ingredients
INSERT INTO ingredients (name, category, unit, stock, min_stock, max_stock, cost_per_unit, supplier, last_restocked, expiry_date, location) VALUES
('Jasmine Rice', 'Dry', 'lbs', 50.0, 20.0, 100.0, 2.99, 'Asian Grocery Co.', '2024-01-05', '2024-12-31', 'Dry Storage A'),
('Chicken Breast', 'Meat', 'lbs', 25.0, 10.0, 50.0, 8.99, 'Fresh Farm Produce', '2024-01-10', '2024-01-17', 'Freezer A'),
('Soy Sauce', 'Liquid', 'bottles', 12.0, 5.0, 25.0, 3.49, 'Asian Grocery Co.', '2024-01-08', '2025-01-08', 'Pantry B'),
('Green Onions', 'Bag', 'bunches', 8.0, 5.0, 15.0, 1.99, 'Fresh Farm Produce', '2024-01-12', '2024-01-19', 'Cooler C'),
('Black Pepper', 'Dry', 'oz', 12.0, 8.0, 30.0, 0.99, 'Spice World', '2024-01-07', '2024-07-07', 'Spice Rack A')
ON CONFLICT DO NOTHING;
`;

async function setupDatabase() {
  try {
    console.log('📋 Creating database tables...');
    
    // Execute the main setup SQL
    const { error: setupError } = await supabase.rpc('exec_sql', { sql: setupSQL });
    
    if (setupError) {
      console.error('❌ Error creating tables:', setupError);
      return;
    }
    
    console.log('✅ Database tables created successfully');
    
    console.log('📊 Inserting sample data...');
    
    // Execute the sample data SQL
    const { error: dataError } = await supabase.rpc('exec_sql', { sql: sampleDataSQL });
    
    if (dataError) {
      console.error('❌ Error inserting sample data:', dataError);
      return;
    }
    
    console.log('✅ Sample data inserted successfully');
    
    // Test the connection by querying a table
    console.log('🔍 Testing database connection...');
    const { data, error } = await supabase.from('products').select('count');
    
    if (error) {
      console.error('❌ Error testing connection:', error);
      return;
    }
    
    console.log('✅ Database setup completed successfully!');
    console.log('🎉 Your inventory management system is ready to use');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

setupDatabase();
