// Square Integration Setup Script
console.log('🟦 Square POS Integration Setup');
console.log('===============================\n');

// Check environment variables
const requiredSquareVars = [
  'SQUARE_ACCESS_TOKEN',
  'SQUARE_LOCATION_ID',
  'SQUARE_ENVIRONMENT'
];

const optionalSquareVars = [
  'SQUARE_APPLICATION_ID'
];

console.log('📋 Required Square Environment Variables:');
let allSquareVarsPresent = true;

requiredSquareVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
    allSquareVarsPresent = false;
  }
});

console.log('\n📋 Optional Square Environment Variables:');
optionalSquareVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`⚠️ ${varName}: NOT SET (optional)`);
  }
});

// Check Supabase vars too
const requiredSupabaseVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

console.log('\n📋 Required Supabase Environment Variables:');
let allSupabaseVarsPresent = true;

requiredSupabaseVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: Set`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
    allSupabaseVarsPresent = false;
  }
});

console.log('\n🎯 Setup Status:');
if (allSquareVarsPresent && allSupabaseVarsPresent) {
  console.log('✅ All required environment variables are set!');
  console.log('🚀 Ready to start app with Square integration enabled');
} else {
  console.log('❌ Missing required environment variables');
  
  if (!allSupabaseVarsPresent) {
    console.log('\n🔧 Supabase Setup Required:');
    console.log('1. Create a Supabase project at https://supabase.com');
    console.log('2. Get your project URL and API keys');
    console.log('3. Run the database schema (supabase-schema.sql)');
  }
  
  if (!allSquareVarsPresent) {
    console.log('\n🟦 Square Setup Required:');
    console.log('1. Create a Square Developer account at https://developer.squareup.com');
    console.log('2. Create a new application');
    console.log('3. Get your Access Token and Location ID');
    console.log('4. Set SQUARE_ENVIRONMENT to "sandbox" for testing');
  }
}

console.log('\n📝 .env.local Template:');
console.log('# Supabase Configuration');
console.log('NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co');
console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key');
console.log('SUPABASE_SERVICE_ROLE_KEY=your_service_key');
console.log('');
console.log('# Square POS Integration');
console.log('SQUARE_ACCESS_TOKEN=your_square_access_token');
console.log('SQUARE_LOCATION_ID=your_square_location_id');
console.log('SQUARE_ENVIRONMENT=sandbox');
console.log('SQUARE_APPLICATION_ID=your_square_app_id');

console.log('\n🚀 Next Steps:');
if (allSquareVarsPresent && allSupabaseVarsPresent) {
  console.log('1. Start the app: npm run dev');
  console.log('2. Test Square sync: Visit /products and click "Sync with Square"');
  console.log('3. Check reports: Visit /reports for Square sales data');
  console.log('4. Monitor console for Square API calls');
} else {
  console.log('1. Add missing environment variables to .env.local');
  console.log('2. Restart the development server');
  console.log('3. Run this script again to verify setup');
}

console.log('\n🔍 Square Features Available:');
console.log('- Product sync from Square catalog');
console.log('- Sales reports from Square orders');
console.log('- Real-time inventory management');
console.log('- Square payment data integration');
console.log('- Automatic product categorization');

console.log('\n📊 API Endpoints:');
console.log('- GET /api/health - Check system status');
console.log('- POST /api/sync/products - Sync products from Square');
console.log('- GET /api/reports - Get sales reports');
console.log('- All endpoints include Square integration status');
