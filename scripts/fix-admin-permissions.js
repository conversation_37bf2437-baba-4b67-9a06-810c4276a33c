// Fix admin <NAME_EMAIL>
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixAdminPermissions() {
  console.log('🔧 Fixing admin <NAME_EMAIL>');
  console.log('===============================================\n');

  try {
    // Check if user exists
    console.log('1. Checking if user exists...');
    const { data: existingUser, error: checkError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking user:', checkError);
      return;
    }

    if (!existingUser) {
      console.log('❌ User <EMAIL> not found in user_profiles table');
      console.log('💡 The user needs to log in first to create their profile');
      console.log('📝 Steps to fix:');
      console.log('   1. Log <NAME_EMAIL>');
      console.log('   2. Run this script again');
      return;
    }

    console.log('✅ User found:', existingUser.email);
    console.log('📋 Current role:', existingUser.role);
    console.log('📅 Created:', existingUser.created_at);

    if (existingUser.role === 'admin') {
      console.log('✅ User already has admin role!');
      console.log('🔍 If you still can\'t see reports, check:');
      console.log('   1. Hard refresh browser (Ctrl+Shift+R)');
      console.log('   2. Clear browser cache');
      console.log('   3. Log out and log back in');
      return;
    }

    // Update role to admin
    console.log('\n2. Updating role to admin...');
    const { data: updatedUser, error: updateError } = await supabase
      .from('user_profiles')
      .update({ 
        role: 'admin',
        updated_at: new Date().toISOString()
      })
      .eq('email', '<EMAIL>')
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating user role:', updateError);
      return;
    }

    console.log('✅ Successfully updated user role!');
    console.log('📋 New role:', updatedUser.role);
    console.log('📅 Updated:', updatedUser.updated_at);

    console.log('\n🎉 Admin permissions fixed!');
    console.log('📝 Next steps:');
    console.log('   1. Log out of the app');
    console.log('   2. Log back <NAME_EMAIL>');
    console.log('   3. You should now see the Reports page');
    console.log('   4. Check navigation for admin role badge');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Also check permissions
async function checkPermissions() {
  console.log('\n🔍 Checking role permissions...');
  
  const rolePermissions = {
    admin: [
      'view_dashboard',
      'view_products', 
      'manage_products',
      'view_ingredients',
      'manage_ingredients',
      'view_reports',
      'sync_square',
      'manage_users'
    ],
    staff: [
      'view_dashboard',
      'view_products',
      'manage_products', 
      'view_ingredients',
      'manage_ingredients'
    ],
    viewer: [
      'view_dashboard',
      'view_products',
      'view_ingredients'
    ]
  };

  console.log('📋 Admin permissions include:');
  rolePermissions.admin.forEach(permission => {
    console.log(`   ✅ ${permission}`);
  });

  console.log('\n🎯 Reports page requires: view_reports permission');
  console.log('🔐 Only admin role has this permission');
}

fixAdminPermissions().then(() => {
  checkPermissions();
});
