// Check environment variables and Supabase connection
console.log('🔍 Environment Check');
console.log('==================\n');

// Check environment variables
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

console.log('📋 Environment Variables:');
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
  }
});

// Check if .env.local exists
const fs = require('fs');
const path = require('path');

const envPath = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envPath)) {
  console.log('\n✅ .env.local file exists');
} else {
  console.log('\n❌ .env.local file NOT FOUND');
  console.log('💡 Create .env.local with your Supabase credentials');
}

console.log('\n🔧 Quick Fix Steps:');
console.log('1. Create .env.local file in project root');
console.log('2. Add your Supabase credentials:');
console.log('   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co');
console.log('   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key');
console.log('   SUPABASE_SERVICE_ROLE_KEY=your_service_key');
console.log('3. Restart the development server');
console.log('4. Check browser console for auth debug messages');

console.log('\n🌐 Browser Debug:');
console.log('- Open browser console (F12)');
console.log('- Look for auth messages starting with 🔍, ✅, or ❌');
console.log('- Check Network tab for failed requests to Supabase');
