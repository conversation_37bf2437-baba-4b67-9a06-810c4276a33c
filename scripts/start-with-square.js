#!/usr/bin/env node

// Start app with Square integration enabled
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Inventory Management App with Square Integration');
console.log('=========================================================\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.log('❌ .env.local file not found');
  console.log('💡 Create .env.local with your configuration');
  console.log('📝 Use .env.example as a template\n');
  process.exit(1);
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Check required variables
const requiredVars = {
  'Supabase': [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ],
  'Square': [
    'SQUARE_ACCESS_TOKEN',
    'SQUARE_LOCATION_ID',
    'SQUARE_ENVIRONMENT'
  ]
};

let allGood = true;

Object.entries(requiredVars).forEach(([service, vars]) => {
  console.log(`📋 ${service} Configuration:`);
  vars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: Set`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      allGood = false;
    }
  });
  console.log('');
});

if (!allGood) {
  console.log('❌ Missing required environment variables');
  console.log('🔧 Run: node scripts/setup-square.js for detailed setup instructions');
  process.exit(1);
}

console.log('✅ All environment variables configured!');
console.log('🟦 Square integration: ENABLED');
console.log('🗄️ Supabase database: ENABLED');
console.log('🔐 Authentication: ENABLED\n');

console.log('🚀 Starting development server...\n');

// Start the Next.js development server
const child = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true
});

child.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
});

child.on('close', (code) => {
  console.log(`\n📊 Server exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  child.kill('SIGTERM');
});
