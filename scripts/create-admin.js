// <PERSON>ript to create an admin user
// Run with: node scripts/create-admin.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  const email = process.argv[2];
  const password = process.argv[3];

  if (!email || !password) {
    console.error('❌ Usage: node scripts/create-admin.js <email> <password>');
    console.error('Example: node scripts/create-admin.js <EMAIL> mypassword123');
    process.exit(1);
  }

  try {
    console.log('🔄 Creating admin user...');
    
    // Create the user with different options
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      phone_confirm: true,
      user_metadata: {
        full_name: 'Admin User',
        role: 'admin'
      }
    });

    if (authError) {
      console.error('❌ Error creating user:', authError.message);
      process.exit(1);
    }

    console.log('✅ User created successfully!');
    console.log('📧 Email:', email);
    console.log('🆔 User ID:', authData.user.id);

    // Update the user profile to admin role
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({ role: 'admin' })
      .eq('id', authData.user.id);

    if (profileError) {
      console.error('⚠️  User created but failed to set admin role:', profileError.message);
      console.log('💡 You can manually set the role in Supabase dashboard or run this SQL:');
      console.log(`UPDATE user_profiles SET role = 'admin' WHERE email = '${email}';`);
    } else {
      console.log('✅ Admin role assigned successfully!');
    }

    console.log('\n🎉 Admin user setup complete!');
    console.log('🌐 You can now login at: http://localhost:3000/login');
    console.log('📧 Email:', email);
    console.log('🔑 Password:', password);

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    process.exit(1);
  }
}

createAdminUser();
