// Test authentication setup
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testAuthSetup() {
  console.log('🔍 Testing authentication setup...\n');

  try {
    // Test 1: Check if user_profiles table exists
    console.log('1. Checking user_profiles table...');
    const { data: tables, error: tableError } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('❌ user_profiles table issue:', tableError.message);
      console.log('💡 Please run the supabase-auth-only.sql file');
      return;
    }
    console.log('✅ user_profiles table exists');

    // Test 2: Check table structure
    console.log('\n2. Checking table structure...');
    const { data: structure, error: structureError } = await supabase
      .rpc('exec', { 
        sql: `
          SELECT column_name, data_type, is_nullable 
          FROM information_schema.columns 
          WHERE table_name = 'user_profiles' 
          ORDER BY ordinal_position;
        `
      });

    if (!structureError && structure) {
      console.log('✅ Table structure:');
      structure.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(required)' : '(optional)'}`);
      });
    }

    // Test 3: Check existing users
    console.log('\n3. Checking existing users...');
    const { data: users, error: usersError } = await supabase
      .from('user_profiles')
      .select('email, role, created_at');

    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message);
    } else {
      console.log(`✅ Found ${users.length} users:`);
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.role})`);
      });
    }

    // Test 4: Check RLS policies
    console.log('\n4. Checking RLS policies...');
    const { data: policies, error: policiesError } = await supabase
      .rpc('exec', {
        sql: `
          SELECT policyname, permissive, roles, cmd 
          FROM pg_policies 
          WHERE tablename = 'user_profiles';
        `
      });

    if (!policiesError && policies) {
      console.log(`✅ Found ${policies.length} RLS policies`);
      policies.forEach(policy => {
        console.log(`   - ${policy.policyname}: ${policy.cmd}`);
      });
    }

    console.log('\n🎉 Authentication setup test complete!');
    console.log('\n📝 Next steps:');
    console.log('1. Try creating a user in Supabase Dashboard (Authentication → Users)');
    console.log('2. Check if profile is auto-created');
    console.log('3. Update role to admin if needed');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

testAuthSetup();
