// Script to list all tables in the database
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function listTables() {
  try {
    console.log('🔍 Listing all tables in the database...\n');
    
    // Query to get all tables in the public schema
    const { data, error } = await supabase
      .rpc('sql', {
        query: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          ORDER BY table_name;
        `
      });
    
    if (error) {
      // Try alternative method
      console.log('Trying alternative method...');
      
      const { data: altData, error: altError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .order('table_name');
      
      if (altError) {
        console.error('❌ Error listing tables:', altError.message);
        console.log('💡 This might be a permissions issue');
        return;
      }
      
      console.log('✅ Found tables:');
      if (altData.length === 0) {
        console.log('   No custom tables found in public schema');
      } else {
        altData.forEach((row, index) => {
          console.log(`   ${index + 1}. ${row.table_name}`);
        });
      }
      return;
    }
    
    console.log('✅ Found tables:');
    if (data.length === 0) {
      console.log('   No custom tables found in public schema');
      console.log('   This means the SQL schema was not executed successfully');
    } else {
      data.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

listTables();
