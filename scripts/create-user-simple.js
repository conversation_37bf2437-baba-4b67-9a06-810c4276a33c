// Simple user creation script using signup
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

// Use anon client for signup (like a normal user would)
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

// Use service client for admin operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createUser() {
  const email = process.argv[2] || '<EMAIL>';
  const password = process.argv[3] || 'admin123';

  console.log('🔄 Creating user via signup...');
  
  try {
    // Method 1: Try normal signup
    const { data: signupData, error: signupError } = await supabaseAnon.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: 'Bryan Cortez',
          role: 'admin'
        }
      }
    });

    if (signupError) {
      console.log('❌ Signup failed:', signupError.message);
      console.log('🔄 Trying admin method...');
      
      // Method 2: Try admin creation
      const { data: adminData, error: adminError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          full_name: 'Bryan Cortez',
          role: 'admin'
        }
      });

      if (adminError) {
        console.error('❌ Admin creation also failed:', adminError.message);
        
        // Method 3: Check if user already exists
        console.log('🔍 Checking if user already exists...');
        const { data: users, error: listError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (!listError) {
          const existingUser = users.users.find(u => u.email === email);
          if (existingUser) {
            console.log('✅ User already exists!');
            console.log('📧 Email:', existingUser.email);
            console.log('🆔 User ID:', existingUser.id);
            
            // Update their role to admin
            const { error: updateError } = await supabaseAdmin
              .from('user_profiles')
              .upsert({
                id: existingUser.id,
                email: existingUser.email,
                full_name: 'Bryan Cortez',
                role: 'admin',
                is_active: true
              });
            
            if (updateError) {
              console.error('❌ Error updating profile:', updateError.message);
            } else {
              console.log('✅ Profile updated to admin role!');
            }
            
            console.log('\n🎉 You can now login at: http://localhost:3000/login');
            console.log('📧 Email:', email);
            console.log('🔑 Password:', password);
            return;
          }
        }
        
        console.error('❌ All methods failed. Please check your Supabase configuration.');
        return;
      }
      
      console.log('✅ User created via admin method!');
      console.log('🆔 User ID:', adminData.user.id);
    } else {
      console.log('✅ User created via signup!');
      console.log('🆔 User ID:', signupData.user?.id);
    }

    // Wait a moment for the trigger to create the profile
    console.log('⏳ Waiting for profile creation...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Update role to admin
    console.log('🔄 Setting admin role...');
    const { error: roleError } = await supabaseAdmin
      .from('user_profiles')
      .update({ role: 'admin' })
      .eq('email', email);

    if (roleError) {
      console.error('⚠️ Error setting admin role:', roleError.message);
      console.log('💡 You can manually set the role in Supabase dashboard');
    } else {
      console.log('✅ Admin role set successfully!');
    }

    console.log('\n🎉 Setup complete!');
    console.log('🌐 Login at: http://localhost:3000/login');
    console.log('📧 Email:', email);
    console.log('🔑 Password:', password);

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

createUser();
