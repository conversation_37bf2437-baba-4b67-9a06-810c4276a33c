-- Supabase Database Schema for Inventory Management System with Square POS Integration
-- Run this in your Supabase SQL editor to create the necessary tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- SQUARE POS INTEGRATION TABLES
-- =============================================

-- Create cached_orders table for storing Square order data
CREATE TABLE IF NOT EXISTS cached_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    square_order_id TEXT UNIQUE NOT NULL,
    location_id TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    state TEXT NOT NULL,
    total_money BIGINT DEFAULT 0,
    total_tax_money BIGINT DEFAULT 0,
    total_discount_money BIGINT DEFAULT 0,
    line_items JSONB DEFAULT '[]'::jsonb,
    fulfillments JSONB DEFAULT '[]'::jsonb,
    tenders JSONB DEFAULT '[]'::jsonb,
    cached_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_state CHECK (state IN ('OPEN', 'COMPLETED', 'CANCELED'))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_cached_orders_square_id ON cached_orders(square_order_id);
CREATE INDEX IF NOT EXISTS idx_cached_orders_location ON cached_orders(location_id);
CREATE INDEX IF NOT EXISTS idx_cached_orders_created_at ON cached_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_cached_orders_state ON cached_orders(state);
CREATE INDEX IF NOT EXISTS idx_cached_orders_cached_at ON cached_orders(cached_at);

-- Create cached_products table for storing Square catalog items
CREATE TABLE IF NOT EXISTS cached_products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    square_catalog_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    category TEXT,
    price BIGINT DEFAULT 0,
    description TEXT,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    cached_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INVENTORY MANAGEMENT TABLES
-- =============================================

-- Products table for inventory management
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    stock INTEGER NOT NULL DEFAULT 0,
    min_stock INTEGER NOT NULL DEFAULT 0,
    max_stock INTEGER DEFAULT NULL,
    description TEXT,
    image_url TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    square_catalog_id TEXT UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_product_status CHECK (status IN ('active', 'inactive', 'discontinued')),
    CONSTRAINT valid_product_category CHECK (category IN ('Rice', 'Noodles', 'Entree', 'Drinks', 'Appetizer', 'Dessert', 'Side'))
);

-- Ingredients table
CREATE TABLE IF NOT EXISTS ingredients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    unit TEXT NOT NULL,
    stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    min_stock DECIMAL(10,2) NOT NULL DEFAULT 0,
    max_stock DECIMAL(10,2) DEFAULT NULL,
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0,
    supplier TEXT,
    last_restocked DATE,
    expiry_date DATE,
    location TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_ingredient_category CHECK (category IN ('All', 'Meat', 'Dry', 'Liquid', 'Bag', 'Vegetables', 'Dairy', 'Spices', 'Frozen'))
);

-- Product ingredients relationship table (recipes)
CREATE TABLE IF NOT EXISTS product_ingredients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id) ON DELETE CASCADE,
    quantity DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(product_id, ingredient_id)
);

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    rating DECIMAL(3,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for cached_products
CREATE INDEX IF NOT EXISTS idx_cached_products_square_id ON cached_products(square_catalog_id);
CREATE INDEX IF NOT EXISTS idx_cached_products_category ON cached_products(category);
CREATE INDEX IF NOT EXISTS idx_cached_products_active ON cached_products(is_active);

-- Create sales_metrics table for storing aggregated daily metrics
CREATE TABLE IF NOT EXISTS sales_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    location_id TEXT NOT NULL,
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    average_order_value DECIMAL(10,2) DEFAULT 0,
    total_tax DECIMAL(10,2) DEFAULT 0,
    total_tips DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, location_id)
);

-- Create indexes for sales_metrics
CREATE INDEX IF NOT EXISTS idx_sales_metrics_date ON sales_metrics(date);
CREATE INDEX IF NOT EXISTS idx_sales_metrics_location ON sales_metrics(location_id);

-- Create sync_logs table to track data synchronization
CREATE TABLE IF NOT EXISTS sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sync_type TEXT NOT NULL,
    status TEXT NOT NULL,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    records_processed INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_sync_type CHECK (sync_type IN ('orders', 'products', 'metrics')),
    CONSTRAINT valid_status CHECK (status IN ('started', 'completed', 'failed'))
);

-- Create index for sync_logs
CREATE INDEX IF NOT EXISTS idx_sync_logs_type_status ON sync_logs(sync_type, status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at);

-- =============================================
-- USER MANAGEMENT AND AUTHENTICATION
-- =============================================

-- User roles enum
CREATE TYPE user_role AS ENUM ('admin', 'staff', 'viewer');

-- User profiles table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    role user_role NOT NULL DEFAULT 'viewer',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for user profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'viewer')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update user profile updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_profile_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updating user profile timestamp
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_user_profile_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cached_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE cached_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS user_role AS $$
BEGIN
    RETURN (
        SELECT role
        FROM user_profiles
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON user_profiles
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can manage all profiles" ON user_profiles
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage user profiles" ON user_profiles
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for cached_orders (all authenticated users can read, admins can manage)
CREATE POLICY "Authenticated users can read cached_orders" ON cached_orders
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage cached_orders" ON cached_orders
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage cached_orders" ON cached_orders
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for cached_products (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read cached_products" ON cached_products
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage cached_products" ON cached_products
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage cached_products" ON cached_products
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for sales_metrics (only admins can access)
CREATE POLICY "Admins can read sales_metrics" ON sales_metrics
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can manage sales_metrics" ON sales_metrics
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage sales_metrics" ON sales_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for sync_logs (only admins can access)
CREATE POLICY "Admins can read sync_logs" ON sync_logs
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can manage sync_logs" ON sync_logs
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage sync_logs" ON sync_logs
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- ADDITIONAL INVENTORY TABLES
-- =============================================

-- Stock movements table for tracking inventory changes
CREATE TABLE IF NOT EXISTS stock_movements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID NOT NULL,
    item_type TEXT NOT NULL,
    item_name TEXT NOT NULL,
    movement_type TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    reason TEXT,
    cost DECIMAL(10,2),
    user_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_item_type CHECK (item_type IN ('product', 'ingredient')),
    CONSTRAINT valid_movement_type CHECK (movement_type IN ('in', 'out', 'adjustment', 'waste', 'expired'))
);

-- Low stock alerts table
CREATE TABLE IF NOT EXISTS low_stock_alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID NOT NULL,
    item_type TEXT NOT NULL,
    item_name TEXT NOT NULL,
    current_stock DECIMAL(10,2) NOT NULL,
    min_stock DECIMAL(10,2) NOT NULL,
    unit TEXT NOT NULL,
    severity TEXT NOT NULL,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_alert_item_type CHECK (item_type IN ('product', 'ingredient')),
    CONSTRAINT valid_severity CHECK (severity IN ('low', 'critical', 'out'))
);

-- Activity logs table for audit trail
CREATE TABLE IF NOT EXISTS activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    action TEXT NOT NULL,
    item_id UUID,
    item_type TEXT,
    item_name TEXT,
    quantity DECIMAL(10,2),
    user_id TEXT,
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_log_item_type CHECK (item_type IN ('product', 'ingredient', 'order', 'system'))
);

-- Sales analytics table for aggregated data
CREATE TABLE IF NOT EXISTS sales_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    location_id TEXT NOT NULL,
    product_id UUID REFERENCES products(id),
    product_name TEXT NOT NULL,
    quantity_sold INTEGER NOT NULL DEFAULT 0,
    revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    profit DECIMAL(10,2) NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(date, location_id, product_id)
);

-- Enable RLS for inventory management tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE low_stock_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for products (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read products" ON products
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage products" ON products
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage products" ON products
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for ingredients (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read ingredients" ON ingredients
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage ingredients" ON ingredients
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage ingredients" ON ingredients
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for product_ingredients (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read product_ingredients" ON product_ingredients
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage product_ingredients" ON product_ingredients
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage product_ingredients" ON product_ingredients
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for suppliers (all authenticated users can read, admins can manage)
CREATE POLICY "Authenticated users can read suppliers" ON suppliers
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage suppliers" ON suppliers
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage suppliers" ON suppliers
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for stock_movements (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read stock_movements" ON stock_movements
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage stock_movements" ON stock_movements
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage stock_movements" ON stock_movements
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for low_stock_alerts (all authenticated users can read, admins and staff can manage)
CREATE POLICY "Authenticated users can read low_stock_alerts" ON low_stock_alerts
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins and staff can manage low_stock_alerts" ON low_stock_alerts
    FOR ALL USING (get_user_role() IN ('admin', 'staff'));

CREATE POLICY "Service role can manage low_stock_alerts" ON low_stock_alerts
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for activity_logs (all authenticated users can read, system manages)
CREATE POLICY "Authenticated users can read activity_logs" ON activity_logs
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Service role can manage activity_logs" ON activity_logs
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for sales_analytics (only admins can access)
CREATE POLICY "Admins can read sales_analytics" ON sales_analytics
    FOR SELECT USING (get_user_role() = 'admin');

CREATE POLICY "Admins can manage sales_analytics" ON sales_analytics
    FOR ALL USING (get_user_role() = 'admin');

CREATE POLICY "Service role can manage sales_analytics" ON sales_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Create a function to clean up old cached data (optional)
CREATE OR REPLACE FUNCTION cleanup_old_cache()
RETURNS void AS $$
BEGIN
    -- Delete cached orders older than 90 days
    DELETE FROM cached_orders
    WHERE cached_at < NOW() - INTERVAL '90 days';

    -- Delete sync logs older than 30 days
    DELETE FROM sync_logs
    WHERE created_at < NOW() - INTERVAL '30 days';

    -- Delete old activity logs older than 1 year
    DELETE FROM activity_logs
    WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

-- Create a function to get sales summary for a date range
CREATE OR REPLACE FUNCTION get_sales_summary(
    start_date DATE,
    end_date DATE,
    location_id_param TEXT DEFAULT NULL
)
RETURNS TABLE (
    total_sales DECIMAL,
    total_orders BIGINT,
    avg_order_value DECIMAL,
    total_tax DECIMAL,
    total_tips DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(sm.total_sales), 0) as total_sales,
        COALESCE(SUM(sm.total_orders), 0) as total_orders,
        CASE 
            WHEN SUM(sm.total_orders) > 0 
            THEN SUM(sm.total_sales) / SUM(sm.total_orders)
            ELSE 0 
        END as avg_order_value,
        COALESCE(SUM(sm.total_tax), 0) as total_tax,
        COALESCE(SUM(sm.total_tips), 0) as total_tips
    FROM sales_metrics sm
    WHERE sm.date >= start_date
        AND sm.date <= end_date
        AND (location_id_param IS NULL OR sm.location_id = location_id_param);
END;
$$ LANGUAGE plpgsql;

-- Function to automatically create low stock alerts
CREATE OR REPLACE FUNCTION check_low_stock()
RETURNS void AS $$
BEGIN
    -- Check products
    INSERT INTO low_stock_alerts (item_id, item_type, item_name, current_stock, min_stock, unit, severity)
    SELECT
        p.id,
        'product',
        p.name,
        p.stock,
        p.min_stock,
        'units',
        CASE
            WHEN p.stock = 0 THEN 'out'
            WHEN p.stock <= p.min_stock * 0.5 THEN 'critical'
            ELSE 'low'
        END
    FROM products p
    WHERE p.stock <= p.min_stock
    AND NOT EXISTS (
        SELECT 1 FROM low_stock_alerts lsa
        WHERE lsa.item_id = p.id
        AND lsa.item_type = 'product'
        AND lsa.is_resolved = false
    );

    -- Check ingredients
    INSERT INTO low_stock_alerts (item_id, item_type, item_name, current_stock, min_stock, unit, severity)
    SELECT
        i.id,
        'ingredient',
        i.name,
        i.stock,
        i.min_stock,
        i.unit,
        CASE
            WHEN i.stock = 0 THEN 'out'
            WHEN i.stock <= i.min_stock * 0.5 THEN 'critical'
            ELSE 'low'
        END
    FROM ingredients i
    WHERE i.stock <= i.min_stock
    AND NOT EXISTS (
        SELECT 1 FROM low_stock_alerts lsa
        WHERE lsa.item_id = i.id
        AND lsa.item_type = 'ingredient'
        AND lsa.is_resolved = false
    );
END;
$$ LANGUAGE plpgsql;

-- Function to update stock and log movement
CREATE OR REPLACE FUNCTION update_stock(
    p_item_id UUID,
    p_item_type TEXT,
    p_quantity DECIMAL,
    p_movement_type TEXT,
    p_reason TEXT DEFAULT NULL,
    p_user_id TEXT DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_item_name TEXT;
    v_unit TEXT;
BEGIN
    -- Get item details and update stock
    IF p_item_type = 'product' THEN
        SELECT name INTO v_item_name FROM products WHERE id = p_item_id;
        v_unit := 'units';

        IF p_movement_type IN ('in', 'adjustment') THEN
            UPDATE products SET stock = stock + p_quantity, updated_at = NOW() WHERE id = p_item_id;
        ELSE
            UPDATE products SET stock = GREATEST(0, stock - p_quantity), updated_at = NOW() WHERE id = p_item_id;
        END IF;

    ELSIF p_item_type = 'ingredient' THEN
        SELECT name, unit INTO v_item_name, v_unit FROM ingredients WHERE id = p_item_id;

        IF p_movement_type IN ('in', 'adjustment') THEN
            UPDATE ingredients SET stock = stock + p_quantity, updated_at = NOW() WHERE id = p_item_id;
        ELSE
            UPDATE ingredients SET stock = GREATEST(0, stock - p_quantity), updated_at = NOW() WHERE id = p_item_id;
        END IF;
    END IF;

    -- Log the movement
    INSERT INTO stock_movements (item_id, item_type, item_name, movement_type, quantity, unit, reason, user_id)
    VALUES (p_item_id, p_item_type, v_item_name, p_movement_type, p_quantity, v_unit, p_reason, p_user_id);

    -- Log the activity
    INSERT INTO activity_logs (action, item_id, item_type, item_name, quantity, user_id)
    VALUES (
        'Stock ' || p_movement_type,
        p_item_id,
        p_item_type,
        v_item_name,
        p_quantity,
        p_user_id
    );

    -- Check for low stock alerts
    PERFORM check_low_stock();
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- SAMPLE DATA (Optional - for testing)
-- =============================================

-- Insert sample suppliers
INSERT INTO suppliers (name, contact_person, email, phone, address, rating, is_active) VALUES
('Asian Grocery Co.', 'John Chen', '<EMAIL>', '******-0101', '123 Asia St, Chinatown', 4.5, true),
('Fresh Farm Produce', 'Mary Johnson', '<EMAIL>', '******-0102', '456 Farm Rd, Valley', 4.2, true),
('Spice World', 'Ahmed Hassan', '<EMAIL>', '******-0103', '789 Spice Ave, Market District', 4.8, true),
('Ocean Fresh Seafood', 'Lisa Wong', '<EMAIL>', '******-0104', '321 Harbor Blvd, Waterfront', 4.6, true)
ON CONFLICT DO NOTHING;

-- Insert sample ingredients
INSERT INTO ingredients (name, category, unit, stock, min_stock, max_stock, cost_per_unit, supplier, last_restocked, expiry_date, location) VALUES
('Jasmine Rice', 'Dry', 'lbs', 50.0, 20.0, 100.0, 2.99, 'Asian Grocery Co.', '2024-01-05', '2024-12-31', 'Dry Storage A'),
('Chicken Breast', 'Meat', 'lbs', 25.0, 10.0, 50.0, 8.99, 'Fresh Farm Produce', '2024-01-10', '2024-01-17', 'Freezer A'),
('Soy Sauce', 'Liquid', 'bottles', 12.0, 5.0, 25.0, 3.49, 'Asian Grocery Co.', '2024-01-08', '2025-01-08', 'Pantry B'),
('Green Onions', 'Bag', 'bunches', 8.0, 5.0, 15.0, 1.99, 'Fresh Farm Produce', '2024-01-12', '2024-01-19', 'Cooler C'),
('Black Pepper', 'Dry', 'oz', 12.0, 8.0, 30.0, 0.99, 'Spice World', '2024-01-07', '2024-07-07', 'Spice Rack A')
ON CONFLICT DO NOTHING;

-- Insert sample products
INSERT INTO products (name, category, price, cost, stock, min_stock, description, status) VALUES
('Chicken Fried Rice', 'Rice', 13.99, 4.80, 18, 5, 'Classic fried rice with tender chicken and fresh vegetables', 'active'),
('Vegetable Fried Rice', 'Rice', 11.99, 3.50, 22, 5, 'Healthy vegetarian fried rice with seasonal vegetables', 'active'),
('Pad Thai', 'Noodles', 14.99, 5.20, 15, 5, 'Traditional Thai stir-fried noodles with tamarind sauce', 'active'),
('Grilled Salmon', 'Entree', 22.99, 12.50, 8, 3, 'Fresh Atlantic salmon grilled to perfection', 'active'),
('Iced Coffee', 'Drinks', 3.99, 1.20, 50, 20, 'Freshly brewed coffee served over ice', 'active')
ON CONFLICT DO NOTHING;

-- Insert some sample data (optional - remove in production)
-- This is just for testing purposes
INSERT INTO sales_metrics (date, location_id, total_sales, total_orders, average_order_value, total_tax, total_tips)
VALUES
    (CURRENT_DATE - INTERVAL '7 days', 'sample_location', 1250.00, 45, 27.78, 125.00, 89.50),
    (CURRENT_DATE - INTERVAL '6 days', 'sample_location', 1450.00, 52, 27.88, 145.00, 102.30),
    (CURRENT_DATE - INTERVAL '5 days', 'sample_location', 1100.00, 38, 28.95, 110.00, 78.20),
    (CURRENT_DATE - INTERVAL '4 days', 'sample_location', 1650.00, 58, 28.45, 165.00, 124.80),
    (CURRENT_DATE - INTERVAL '3 days', 'sample_location', 1350.00, 47, 28.72, 135.00, 95.40),
    (CURRENT_DATE - INTERVAL '2 days', 'sample_location', 1550.00, 55, 28.18, 155.00, 118.60),
    (CURRENT_DATE - INTERVAL '1 day', 'sample_location', 1750.00, 62, 28.23, 175.00, 135.20)
ON CONFLICT (date, location_id) DO NOTHING;

-- =============================================
-- AUTHENTICATION SETUP INSTRUCTIONS
-- =============================================

/*
IMPORTANT: After running this schema, you need to set up authentication:

1. In your Supabase dashboard, go to Authentication > Settings
2. Enable email authentication
3. Set up your site URL and redirect URLs
4. Create your first admin user:
   - Go to Authentication > Users
   - Click "Add user"
   - Enter email and password
   - After creating the user, run this SQL to make them an admin:

   UPDATE user_profiles
   SET role = 'admin'
   WHERE email = '<EMAIL>';

5. User Roles:
   - admin: Full access to everything including reports, sync, and user management
   - staff: Can restock, view stock, manage products/ingredients, but no reports or sync
   - viewer: Read-only access to products and ingredients, no reports or sync

6. The system will automatically create a user_profile when someone signs up
   with the default role of 'viewer'. Admins can change user roles as needed.
*/
