'use client';

import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { formatCurrency } from '#/lib/square-client';

interface SalesChartProps {
  data: Array<{
    date: string;
    sales: number;
    orders: number;
  }>;
  type?: 'daily' | 'hourly';
}

export function SalesChart({ data, type = 'daily' }: SalesChartProps) {
  const formatXAxisLabel = (value: string) => {
    if (type === 'hourly') {
      return value; // Already formatted as "HH:00"
    }
    return new Date(value).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">
            {type === 'hourly' ? `${label}` : formatXAxisLabel(label)}
          </p>
          <p className="text-green-600">
            Sales: {formatCurrency(payload[0].value)}
          </p>
          <p className="text-blue-600">
            Orders: {payload[1]?.value || 0}
          </p>
        </div>
      );
    }
    return null;
  };

  if (data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        No data available for the selected period
      </div>
    );
  }

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        {type === 'hourly' ? (
          <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatXAxisLabel}
              fontSize={12}
              stroke="#666"
            />
            <YAxis 
              yAxisId="sales"
              orientation="left"
              tickFormatter={(value) => `$${value}`}
              fontSize={12}
              stroke="#666"
            />
            <YAxis 
              yAxisId="orders"
              orientation="right"
              fontSize={12}
              stroke="#666"
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              yAxisId="sales"
              dataKey="sales" 
              fill="#10b981" 
              name="Sales"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        ) : (
          <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatXAxisLabel}
              fontSize={12}
              stroke="#666"
            />
            <YAxis 
              yAxisId="sales"
              orientation="left"
              tickFormatter={(value) => `$${value}`}
              fontSize={12}
              stroke="#666"
            />
            <YAxis 
              yAxisId="orders"
              orientation="right"
              fontSize={12}
              stroke="#666"
            />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              yAxisId="sales"
              type="monotone" 
              dataKey="sales" 
              stroke="#10b981" 
              strokeWidth={3}
              dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
            />
            <Line 
              yAxisId="orders"
              type="monotone" 
              dataKey="orders" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
              strokeDasharray="5 5"
            />
          </LineChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
