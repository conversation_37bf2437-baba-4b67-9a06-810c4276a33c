'use client';

import { But<PERSON> } from '#/ui/button';
import { useState } from 'react';
import { AdminOnly } from '#/components/auth/role-guard';

export function SyncButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSync = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/sync/products', { method: 'POST' });
      const data = await response.json();
      
      if (data.success) {
        alert(`Successfully synced ${data.count} products from Square!`);
        window.location.reload();
      } else {
        alert(`Sync failed: ${data.error}`);
      }
    } catch (error) {
      alert(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminOnly>
      <Button
        className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50"
        onClick={handleSync}
        disabled={isLoading}
      >
        {isLoading ? '🔄 Syncing...' : '🔄 Sync from Square'}
      </Button>
    </AdminOnly>
  );
}
