'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { formatCurrency } from '#/lib/square-client';

interface PaymentMethod {
  method: string;
  amount: number;
  count: number;
}

interface PaymentMethodsChartProps {
  data: PaymentMethod[];
}

const COLORS = [
  '#3b82f6', // Blue
  '#10b981', // Green
  '#f59e0b', // Yellow
  '#ef4444', // Red
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#84cc16', // Lime
  '#f97316', // Orange
];

const formatPaymentMethod = (method: string) => {
  const methodMap: { [key: string]: string } = {
    'CARD': 'Credit/Debit Card',
    'CASH': 'Cash',
    'SQUARE_GIFT_CARD': 'Gift Card',
    'BANK_ACCOUNT': 'Bank Transfer',
    'WALLET': 'Digital Wallet',
    'BUY_NOW_PAY_LATER': 'Buy Now Pay Later',
    'OTHER': 'Other',
  };
  
  return methodMap[method] || method;
};

export function PaymentMethodsChart({ data }: PaymentMethodsChartProps) {
  if (data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        No payment data available
      </div>
    );
  }

  const chartData = data.map((item, index) => ({
    ...item,
    name: formatPaymentMethod(item.method),
    color: COLORS[index % COLORS.length],
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.name}</p>
          <p className="text-green-600">
            Amount: {formatCurrency(data.amount)}
          </p>
          <p className="text-blue-600">
            Transactions: {data.count}
          </p>
          <p className="text-gray-600">
            Avg: {formatCurrency(data.amount / data.count)}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Don't show labels for slices smaller than 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="space-y-4">
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={CustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="amount"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      {/* Legend with detailed info */}
      <div className="grid grid-cols-1 gap-2 text-sm">
        {chartData.map((item, index) => (
          <div key={item.method} className="flex items-center justify-between p-2 bg-gray-50 rounded">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: item.color }}
              />
              <span className="font-medium">{item.name}</span>
            </div>
            <div className="text-right">
              <div className="font-semibold text-green-600">
                {formatCurrency(item.amount)}
              </div>
              <div className="text-xs text-gray-500">
                {item.count} transactions
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
