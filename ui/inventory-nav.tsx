'use client';

import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { useAuth } from '#/lib/auth-context';
import { RoleGuard } from '#/components/auth/role-guard';
import { Permission } from '#/lib/auth-types';

const navigation = [
  { name: 'Dashboard', href: '/', icon: '📊', permission: 'view_dashboard' as Permission },
  { name: 'Products', href: '/products', icon: '🍽️', permission: 'view_products' as Permission },
  { name: 'Ingredients', href: '/ingredients', icon: '🥬', permission: 'view_ingredients' as Permission },
  { name: 'Reports', href: '/reports', icon: '📈', permission: 'view_reports' as Permission },
  { name: 'API Docs', href: '/api-docs', icon: '📚', permission: 'view_dashboard' as Permission },
  { name: 'API Test', href: '/api-test', icon: '🧪', permission: 'view_dashboard' as Permission },
];

export function InventoryNav() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, profile, signOut, loading } = useAuth();

  return (
    <nav className="bg-white shadow">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 justify-between">
          <div className="flex">
            <div className="flex flex-shrink-0 items-center">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-2xl">🏪</span>
                <span className="text-xl font-bold text-gray-900">Inventory Manager</span>
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navigation.map((item) => (
                <RoleGuard key={item.name} permissions={item.permission}>
                  <Link
                    href={item.href}
                    className={clsx(
                      'inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium',
                      pathname === item.href
                        ? 'border-blue-500 text-gray-900'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                    )}
                  >
                    <span className="mr-2">{item.icon}</span>
                    {item.name}
                  </Link>
                </RoleGuard>
              ))}
            </div>
          </div>
          
          <div className="hidden sm:ml-6 sm:flex sm:items-center space-x-4">
            {loading ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            ) : user && profile ? (
              <>
                <div className="text-sm text-gray-700">
                  <span className="font-medium">{profile.full_name || user.email}</span>
                  <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    profile.role === 'admin'
                      ? 'bg-blue-100 text-blue-800'
                      : profile.role === 'staff'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {profile.role}
                  </span>
                </div>
                <button
                  onClick={signOut}
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <Link
                href="/login"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Sign In
              </Link>
            )}
          </div>
          
          <div className="-mr-2 flex items-center sm:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={clsx('sm:hidden', mobileMenuOpen ? 'block' : 'hidden')} id="mobile-menu">
        <div className="space-y-1 pb-3 pt-2">
          {navigation.map((item) => (
            <RoleGuard key={item.name} permissions={item.permission}>
              <Link
                href={item.href}
                className={clsx(
                  'block border-l-4 py-2 pl-3 pr-4 text-base font-medium',
                  pathname === item.href
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800'
                )}
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="mr-2">{item.icon}</span>
                {item.name}
              </Link>
            </RoleGuard>
          ))}

          {/* Mobile user info and auth controls */}
          <div className="border-t border-gray-200 pt-4 pb-3">
            {loading ? (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : user && profile ? (
              <>
                <div className="px-4 py-2">
                  <div className="text-base font-medium text-gray-800">
                    {profile.full_name || user.email}
                  </div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                  <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    profile.role === 'admin'
                      ? 'bg-blue-100 text-blue-800'
                      : profile.role === 'staff'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {profile.role}
                  </span>
                </div>
                <button
                  onClick={() => {
                    signOut();
                    setMobileMenuOpen(false);
                  }}
                  className="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <Link
                href="/login"
                className="block px-4 py-2 text-base font-medium text-blue-600 hover:text-blue-800 hover:bg-gray-100"
                onClick={() => setMobileMenuOpen(false)}
              >
                Sign In
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
