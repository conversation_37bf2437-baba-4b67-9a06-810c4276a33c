'use client';

import { Button } from '#/ui/button';

interface DateRangePickerProps {
  startDate: string;
  endDate: string;
  onDateChange: (startDate: string, endDate: string) => void;
  className?: string;
}

export function DateRangePicker({ startDate, endDate, onDateChange, className }: DateRangePickerProps) {
  // Helper function to format date for input
  const formatDateForInput = (dateString: string) => {
    return dateString.split('T')[0];
  };

  // Helper function to convert input date to ISO string
  const formatDateToISO = (dateString: string, isEndDate = false) => {
    const date = new Date(dateString);
    if (isEndDate) {
      date.setHours(23, 59, 59, 999);
    } else {
      date.setHours(0, 0, 0, 0);
    }
    return date.toISOString();
  };

  // Preset date ranges
  const presets = [
    {
      label: 'Last 7 days',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 7);
        return {
          start: formatDateToISO(start.toISOString().split('T')[0]),
          end: formatDateToISO(end.toISOString().split('T')[0], true)
        };
      }
    },
    {
      label: 'Last 2 weeks',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 14);
        return {
          start: formatDateToISO(start.toISOString().split('T')[0]),
          end: formatDateToISO(end.toISOString().split('T')[0], true)
        };
      }
    },
    {
      label: 'Last 30 days',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 30);
        return {
          start: formatDateToISO(start.toISOString().split('T')[0]),
          end: formatDateToISO(end.toISOString().split('T')[0], true)
        };
      }
    },
    {
      label: 'This month',
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth(), 1);
        const end = new Date();
        return {
          start: formatDateToISO(start.toISOString().split('T')[0]),
          end: formatDateToISO(end.toISOString().split('T')[0], true)
        };
      }
    }
  ];

  return (
    <div className={`space-y-4 ${className || ''}`}>
      <div className="flex flex-wrap gap-2">
        {presets.map((preset) => (
          <Button
            key={preset.label}
            onClick={() => {
              const { start, end } = preset.getValue();
              onDateChange(start, end);
            }}
            className="text-xs px-3 py-1 border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 rounded-md transition-colors"
          >
            {preset.label}
          </Button>
        ))}
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex flex-col gap-2">
          <label htmlFor="start-date" className="text-sm font-medium">
            Start Date
          </label>
          <input
            id="start-date"
            type="date"
            value={formatDateForInput(startDate)}
            onChange={(e) => {
              const newStartDate = formatDateToISO(e.target.value);
              onDateChange(newStartDate, endDate);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="end-date" className="text-sm font-medium">
            End Date
          </label>
          <input
            id="end-date"
            type="date"
            value={formatDateForInput(endDate)}
            onChange={(e) => {
              const newEndDate = formatDateToISO(e.target.value, true);
              onDateChange(startDate, newEndDate);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  );
}
