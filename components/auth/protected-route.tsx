'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '#/lib/auth-context';
import { UserRole, Permission } from '#/lib/auth-types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: UserRole | UserRole[];
  permissions?: Permission | Permission[];
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  roles, 
  permissions, 
  redirectTo = '/unauthorized' 
}: ProtectedRouteProps) {
  const { user, hasRole, canAccess, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading) return;

    // Redirect to login if not authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Check role-based access
    if (roles && !hasRole(roles)) {
      router.push(redirectTo);
      return;
    }

    // Check permission-based access
    if (permissions) {
      const permissionArray = Array.isArray(permissions) ? permissions : [permissions];
      const hasAllPermissions = permissionArray.every(permission => canAccess(permission));
      
      if (!hasAllPermissions) {
        router.push(redirectTo);
        return;
      }
    }
  }, [user, loading, hasRole, canAccess, roles, permissions, router, redirectTo]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render anything while redirecting
  if (!user) {
    return null;
  }

  // Check access again before rendering
  if (roles && !hasRole(roles)) {
    return null;
  }

  if (permissions) {
    const permissionArray = Array.isArray(permissions) ? permissions : [permissions];
    const hasAllPermissions = permissionArray.every(permission => canAccess(permission));
    
    if (!hasAllPermissions) {
      return null;
    }
  }

  return <>{children}</>;
}
