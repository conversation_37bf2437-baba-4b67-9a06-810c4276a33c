'use client';

import { useAuth } from '#/lib/auth-context';
import { UserRole, Permission } from '#/lib/auth-types';

interface RoleGuardProps {
  children: React.ReactNode;
  roles?: UserRole | UserRole[];
  permissions?: Permission | Permission[];
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

export function RoleGuard({ 
  children, 
  roles, 
  permissions, 
  fallback = null,
  requireAuth = true 
}: RoleGuardProps) {
  const { user, hasRole, canAccess, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Check authentication requirement
  if (requireAuth && !user) {
    return <>{fallback}</>;
  }

  // Check role-based access
  if (roles) {
    if (!hasRole(roles)) {
      return <>{fallback}</>;
    }
  }

  // Check permission-based access
  if (permissions) {
    const permissionArray = Array.isArray(permissions) ? permissions : [permissions];
    const hasAllPermissions = permissionArray.every(permission => canAccess(permission));
    
    if (!hasAllPermissions) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles="admin" fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function StaffOrAdmin({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard roles={['admin', 'staff']} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AuthenticatedOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard requireAuth={true} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}
