'use client';

import { useState, useEffect } from 'react';
import { ingredientsService } from '#/lib/ingredients-service';

interface AddProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (product: NewProduct) => void;
}

interface NewProduct {
  name: string;
  category: string;
  description: string;
  image_url: string;
  ingredients: ProductIngredient[];
}

interface ProductIngredient {
  ingredient_id: string;
  ingredient_name: string;
  quantity: number;
  unit: string;
}

const categories = ['Rice', 'Noodles', 'Entree', 'Drinks', 'Appetizer', 'Dessert', 'Side'];

export function AddProductModal({ isOpen, onClose, onSubmit }: AddProductModalProps) {
  const [formData, setFormData] = useState<NewProduct>({
    name: '',
    category: 'Entree',
    description: '',
    image_url: '',
    ingredients: []
  });
  const [availableIngredients, setAvailableIngredients] = useState<{
    id: string;
    name: string;
    unit: string;
  }[]>([]);

  // Load available ingredients
  useEffect(() => {
    const loadIngredients = async () => {
      try {
        console.log('🔍 ADD MODAL: Loading ingredients for add modal...');
        const ingredients = await ingredientsService.getAllIngredients();
        console.log('✅ ADD MODAL: Loaded ingredients for add modal:', ingredients.length, ingredients);
        console.log('🔍 ADD MODAL: First ingredient:', ingredients[0]);
        setAvailableIngredients(ingredients);
      } catch (error) {
        console.error('❌ ADD MODAL: Error loading ingredients:', error);
        setAvailableIngredients([]); // Ensure empty array on error
      }
    };

    if (isOpen) {
      console.log('🚀 ADD MODAL: Modal opened, loading ingredients...');
      loadIngredients();
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Convert camelCase to snake_case for API
    const apiData = {
      name: formData.name,
      category: formData.category,
      description: formData.description,
      image_url: formData.image_url,
      ingredients: formData.ingredients
    };

    onSubmit(apiData);
    // Reset form
    setFormData({
      name: '',
      category: 'Entree',
      description: '',
      image_url: '',
      ingredients: []
    });
    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      // Handle number inputs - remove leading zeros but preserve decimal patterns
      let processedValue = value;

      // Remove leading zeros except for "0" and "0." patterns
      if (processedValue.length > 1 && processedValue.startsWith('0') && !processedValue.startsWith('0.')) {
        processedValue = processedValue.replace(/^0+/, '') || '0';
      }

      // Convert to number for storage
      const numericValue = processedValue === '' ? 0 : parseFloat(processedValue);
      const finalValue = isNaN(numericValue) ? 0 : numericValue;

      setFormData(prev => ({
        ...prev,
        [name]: finalValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const addIngredient = () => {
    setFormData(prev => ({
      ...prev,
      ingredients: [...prev.ingredients, { ingredient_id: '', ingredient_name: '', quantity: 0, unit: '' }]
    }));
  };

  const removeIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  const updateIngredient = (index: number, field: keyof ProductIngredient, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.map((ing, i) =>
        i === index ? { ...ing, [field]: value } : ing
      )
    }));
  };

  const handleIngredientSelect = (index: number, ingredientId: string) => {
    const selectedIngredient = availableIngredients.find(ing => ing.id === ingredientId);
    if (selectedIngredient) {
      updateIngredient(index, 'ingredient_id', ingredientId);
      updateIngredient(index, 'ingredient_name', selectedIngredient.name);
      updateIngredient(index, 'unit', selectedIngredient.unit);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Add New Product</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Chicken Fried Rice"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category *
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Product description..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Image URL
                  </label>
                  <input
                    type="url"
                    name="image_url"
                    value={formData.image_url}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </div>

            </div>

            {/* Ingredients Section */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">Ingredients</h3>
                <button
                  type="button"
                  onClick={addIngredient}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Add Ingredient
                </button>
              </div>

              {formData.ingredients.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No ingredients added yet. Click "Add Ingredient" to start building your recipe.
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Header row with labels - only shown once */}
                  <div className="flex gap-3 items-center px-3 py-2 bg-gray-50 rounded-md">
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">Ingredient</span>
                    </div>
                    <div className="w-24">
                      <span className="text-sm font-medium text-gray-700">Quantity</span>
                    </div>
                    <div className="w-20">
                      <span className="text-sm font-medium text-gray-700">Unit</span>
                    </div>
                    <div className="w-16">
                      <span className="text-sm font-medium text-gray-700">Action</span>
                    </div>
                  </div>

                  {formData.ingredients.map((ingredient, index) => (
                    <div key={index} className="flex gap-3 items-center p-3 border border-gray-200 rounded-md">
                      <div className="flex-1">
                        <select
                          value={ingredient.ingredient_id}
                          onChange={(e) => handleIngredientSelect(index, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select ingredient...</option>
                          {availableIngredients.map(ing => (
                            <option key={ing.id} value={ing.id}>{ing.name}</option>
                          ))}
                        </select>
                      </div>
                      <div className="w-24">
                        <input
                          type="number"
                          value={ingredient.quantity || ''}
                          onChange={(e) => {
                            let value = e.target.value;
                            // Remove leading zeros but preserve "0" and "0." patterns
                            if (value.length > 1 && value.startsWith('0') && !value.startsWith('0.')) {
                              value = value.replace(/^0+/, '') || '0';
                            }
                            const numericValue = value === '' ? 0 : parseFloat(value);
                            const finalValue = isNaN(numericValue) ? 0 : numericValue;
                            updateIngredient(index, 'quantity', finalValue);
                          }}
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0"
                        />
                      </div>
                      <div className="w-20">
                        <input
                          type="text"
                          value={ingredient.unit}
                          onChange={(e) => updateIngredient(index, 'unit', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="unit"
                          readOnly
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => removeIngredient(index)}
                        className="px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Add Product
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
