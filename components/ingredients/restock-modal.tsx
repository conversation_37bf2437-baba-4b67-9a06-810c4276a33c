'use client';

import { useState, useEffect } from 'react';
import { ingredientsService, PriceStats } from '#/lib/ingredients-service';
import { suppliersService } from '#/lib/suppliers-service';

interface RestockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (restockData: RestockData) => void;
  ingredient: {
    id: string;
    name: string;
    unit: string;
    stock: number;
    cost_per_unit: number;
    supplier?: string;
  };
}

interface RestockData {
  ingredientId: string;
  ingredientName: string;
  currentStock: number;
  addAmount: number;
  newStock: number;
  unit: string;
  costPerUnit: number;
  totalCost: number;
  supplier: string;
  notes: string;
  restockDate: string;
}

export function RestockModal({ isOpen, onClose, onSubmit, ingredient }: RestockModalProps) {
  const [formData, setFormData] = useState<RestockData>({
    ingredientId: '',
    ingredientName: '',
    currentStock: 0,
    addAmount: 0,
    newStock: 0,
    unit: '',
    costPerUnit: 0,
    totalCost: 0,
    supplier: '',
    notes: '',
    restockDate: '' // Will be set when component mounts
  });

  const [priceStats, setPriceStats] = useState<PriceStats | null>(null);
  const [priceTrend, setPriceTrend] = useState<{
    currentPrice: number;
    weeklyLow: number;
    monthlyLow: number;
    trend: string;
    savings: number;
    recommendation: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<string[]>([]);

  // Set today's date and load suppliers when modal opens
  useEffect(() => {
    if (isOpen) {
      const today = new Date().toISOString().split('T')[0];
      setFormData(prev => ({
        ...prev,
        restockDate: today
      }));

      // Load suppliers
      loadSuppliers();
    }
  }, [isOpen]);

  const loadSuppliers = async () => {
    try {
      const supplierNames = await suppliersService.getSupplierNames();
      setSuppliers(supplierNames);
    } catch (error) {
      console.error('Error loading suppliers:', error);
    }
  };

  // Update form data when ingredient changes and load price history
  useEffect(() => {
    if (ingredient && isOpen) {
      const currentStock = ingredient.stock || 0;
      setFormData(prev => ({
        ...prev,
        ingredientId: ingredient.id,
        ingredientName: ingredient.name || '',
        currentStock: currentStock,
        newStock: currentStock, // Will be updated when addAmount changes
        unit: ingredient.unit || '',
        costPerUnit: ingredient.costPerUnit || 0,
        supplier: ingredient.supplier || '',
        totalCost: 0
      }));

      // Load price statistics and trends
      loadPriceData(ingredient.id);
    }
  }, [ingredient, isOpen]);

  const loadPriceData = async (ingredientId: string) => {
    setLoading(true);
    try {
      const [stats, trend] = await Promise.all([
        ingredientsService.getPriceStats(ingredientId, 30),
        ingredientsService.getPriceTrendAnalysis(ingredientId)
      ]);
      setPriceStats(stats);
      setPriceTrend(trend);
    } catch (error) {
      console.error('Error loading price data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update new stock and total cost when add amount or cost per unit changes
  useEffect(() => {
    const newStock = formData.currentStock + formData.addAmount;
    const totalCost = formData.addAmount * formData.costPerUnit;
    setFormData(prev => ({
      ...prev,
      newStock,
      totalCost
    }));
  }, [formData.addAmount, formData.costPerUnit, formData.currentStock]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    // Reset form
    const today = new Date().toISOString().split('T')[0];
    setFormData(prev => ({
      ...prev,
      addAmount: 0,
      totalCost: 0,
      notes: '',
      restockDate: today
    }));
    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      // Handle number inputs - remove leading zeros but preserve decimal patterns
      let processedValue = value;

      // Remove leading zeros except for "0" and "0." patterns
      if (processedValue.length > 1 && processedValue.startsWith('0') && !processedValue.startsWith('0.')) {
        processedValue = processedValue.replace(/^0+/, '') || '0';
      }

      // Convert to number for storage
      const numericValue = processedValue === '' ? 0 : parseFloat(processedValue);
      const finalValue = isNaN(numericValue) ? 0 : numericValue;

      setFormData(prev => ({
        ...prev,
        [name]: finalValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Restock Ingredient</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Ingredient Info */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{formData.ingredientName}</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Current Stock:</span>
                  <span className="ml-2 font-medium">{formData.currentStock} {formData.unit}</span>
                </div>
                <div>
                  <span className="text-gray-500">Current Price:</span>
                  <span className="ml-2 font-medium">${formData.costPerUnit}</span>
                </div>
              </div>
            </div>

            {/* Price History & Trends */}
            {loading ? (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-center text-blue-600">Loading price history...</div>
              </div>
            ) : priceStats && priceTrend ? (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="text-lg font-semibold text-blue-900 mb-3">Price Analysis (Last 30 Days)</h4>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-sm text-blue-700">Weekly Low</div>
                    <div className="text-lg font-bold text-green-600">${priceTrend.weeklyLow.toFixed(2)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-blue-700">Monthly Low</div>
                    <div className="text-lg font-bold text-green-600">${priceTrend.monthlyLow.toFixed(2)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-blue-700">Average</div>
                    <div className="text-lg font-bold text-blue-600">${priceStats.average_price.toFixed(2)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-blue-700">Trend</div>
                    <div className={`text-sm font-medium ${
                      priceStats.price_trend === 'increasing' ? 'text-red-600' :
                      priceStats.price_trend === 'decreasing' ? 'text-green-600' :
                      'text-gray-600'
                    }`}>
                      {priceStats.price_trend === 'increasing' ? '📈 Rising' :
                       priceStats.price_trend === 'decreasing' ? '📉 Falling' :
                       priceStats.price_trend === 'stable' ? '➡️ Stable' : '📊 Variable'}
                    </div>
                  </div>
                </div>

                {priceTrend.savings !== 0 && (
                  <div className={`p-3 rounded-md ${
                    priceTrend.savings > 0 ? 'bg-yellow-100 border border-yellow-300' : 'bg-green-100 border border-green-300'
                  }`}>
                    <div className="text-sm font-medium">
                      {priceTrend.savings > 0 ? '💡 Savings Opportunity' : '✅ Good Price'}
                    </div>
                    <div className="text-sm">
                      {priceTrend.recommendation}
                      {priceTrend.savings > 0 && (
                        <span className="ml-2 font-medium">
                          (Save ${Math.abs(priceTrend.savings).toFixed(2)} per unit)
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : null}

            {/* Restock Details */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount to Add *
                </label>
                <div className="relative">
                  <input
                    type="number"
                    name="addAmount"
                    value={formData.addAmount || ''}
                    onChange={handleChange}
                    onWheel={(e) => e.currentTarget.blur()} // Prevent scroll wheel changes
                    min="0"
                    step="0.01"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    placeholder="0"
                  />
                  <span className="absolute right-3 top-2 text-gray-500 text-sm">{formData.unit}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cost per Unit ($)
                </label>
                <input
                  type="number"
                  name="costPerUnit"
                  value={formData.costPerUnit || ''}
                  onChange={handleChange}
                  onWheel={(e) => e.currentTarget.blur()} // Prevent scroll wheel changes
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Supplier
                </label>
                <select
                  name="supplier"
                  value={formData.supplier}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="">Select a supplier...</option>
                  {suppliers.map(supplier => (
                    <option key={supplier} value={supplier}>{supplier}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Restock Date *
                </label>
                <input
                  type="date"
                  name="restockDate"
                  value={formData.restockDate}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Any additional notes about this restock..."
                />
              </div>
            </div>

            {/* Summary */}
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="text-lg font-semibold text-green-900 mb-2">Restock Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-700">Current Stock:</span>
                  <span className="font-medium">{formData.currentStock} {formData.unit}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700">Adding:</span>
                  <span className="font-medium">+{formData.addAmount} {formData.unit}</span>
                </div>
                <div className="flex justify-between border-t border-green-200 pt-2">
                  <span className="text-green-700 font-medium">New Stock:</span>
                  <span className="font-bold">{formData.newStock} {formData.unit}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700 font-medium">Total Cost:</span>
                  <span className="font-bold">${formData.totalCost.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
              >
                Confirm Restock
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
