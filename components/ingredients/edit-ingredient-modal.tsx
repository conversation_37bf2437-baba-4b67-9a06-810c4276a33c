'use client';

import { useState, useEffect } from 'react';
import { suppliersService } from '#/lib/suppliers-service';

interface EditIngredientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (ingredient: EditIngredient) => void;
  ingredient: {
    id: string;
    name: string;
    category: string;
    unit: string;
    stock: number;
    min_stock: number;
    max_stock?: number;
    cost_per_unit: number;
    supplier?: string;
    location?: string;
    created_at: string;
    updated_at: string;
  };
}

interface EditIngredient {
  id: string;
  name: string;
  category: string;
  unit: string;
  stock: number;
  min_stock: number;
  max_stock: number;
  cost_per_unit: number;
  supplier: string;
  location: string;
  expiryDate: string;
}

const categories = ['All', 'Meat', 'Dry', 'Liquid', 'Bag', 'Vegetables', 'Dairy', 'Spices', 'Frozen'];
const units = ['lbs', 'kg', 'oz', 'grams', 'bottles', 'cans', 'bunches', 'pieces', 'liters', 'gallons'];
const locations = ['Dry Storage A', 'Dry Storage B', 'Freezer A', 'Freezer B', 'Cooler A', 'Cooler B', 'Pantry A', 'Pantry B', 'Spice Rack A'];

export function EditIngredientModal({ isOpen, onClose, onSubmit, ingredient }: EditIngredientModalProps) {
  const [formData, setFormData] = useState<EditIngredient>({
    id: '',
    name: '',
    category: 'All',
    unit: 'lbs',
    stock: 0,
    min_stock: 0,
    max_stock: 0,
    cost_per_unit: 0,
    supplier: '',
    location: 'Dry Storage A',
    expiryDate: ''
  });

  const [suppliers, setSuppliers] = useState<string[]>([]);

  // Load suppliers when modal opens
  useEffect(() => {
    if (isOpen) {
      loadSuppliers();
    }
  }, [isOpen]);

  const loadSuppliers = async () => {
    try {
      const supplierNames = await suppliersService.getSupplierNames();
      setSuppliers(supplierNames);
    } catch (error) {
      console.error('Error loading suppliers:', error);
    }
  };

  // Update form data when ingredient changes
  useEffect(() => {
    if (ingredient) {
      setFormData({
        id: ingredient.id,
        name: ingredient.name || '',
        category: ingredient.category || 'All',
        unit: ingredient.unit || 'lbs',
        stock: ingredient.stock || 0,
        min_stock: ingredient.min_stock || 0,
        max_stock: ingredient.max_stock || 0,
        cost_per_unit: ingredient.cost_per_unit || 0,
        supplier: ingredient.supplier || '',
        location: ingredient.location || 'Dry Storage A',
        expiryDate: ingredient.expiryDate || ''
      });
    }
  }, [ingredient]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Convert camelCase to snake_case for API (keep the ID as is)
    const apiData = {
      id: formData.id,
      name: formData.name,
      category: formData.category,
      unit: formData.unit,
      stock: formData.stock,
      min_stock: formData.min_stock,
      max_stock: formData.max_stock,
      cost_per_unit: formData.cost_per_unit,
      supplier: formData.supplier,
      location: formData.location,
      expiryDate: formData.expiryDate
    };

    onSubmit(apiData);
    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'number') {
      // Handle number inputs - remove leading zeros but preserve decimal patterns
      let processedValue = value;

      // Remove leading zeros except for "0" and "0." patterns
      if (processedValue.length > 1 && processedValue.startsWith('0') && !processedValue.startsWith('0.')) {
        processedValue = processedValue.replace(/^0+/, '') || '0';
      }

      // Convert to number for storage
      const numericValue = processedValue === '' ? 0 : parseFloat(processedValue);
      const finalValue = isNaN(numericValue) ? 0 : numericValue;

      setFormData(prev => ({
        ...prev,
        [name]: finalValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Edit Ingredient</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ingredient Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category *
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Unit *
                  </label>
                  <select
                    name="unit"
                    value={formData.unit}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Supplier
                  </label>
                  <select
                    name="supplier"
                    value={formData.supplier}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a supplier...</option>
                    {suppliers.map(supplier => (
                      <option key={supplier} value={supplier}>{supplier}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Stock & Pricing */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Stock & Pricing</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Stock *
                  </label>
                  <input
                    type="number"
                    name="stock"
                    value={formData.stock || ''}
                    onChange={handleChange}
                    onWheel={(e) => e.currentTarget.blur()}
                    min="0"
                    step="0.01"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Stock *
                  </label>
                  <input
                    type="number"
                    name="min_stock"
                    value={formData.min_stock || ''}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Stock
                  </label>
                  <input
                    type="number"
                    name="max_stock"
                    value={formData.max_stock || ''}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cost per Unit ($) *
                  </label>
                  <input
                    type="number"
                    name="cost_per_unit"
                    value={formData.cost_per_unit || ''}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Storage & Expiry */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Location *
                </label>
                <select
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expiry Date
                </label>
                <input
                  type="date"
                  name="expiryDate"
                  value={formData.expiryDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
              >
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
