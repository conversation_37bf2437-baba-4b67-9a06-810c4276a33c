'use client';

import { AuthenticatedOnly } from '#/components/auth/role-guard';

interface IngredientsWrapperProps {
  children: React.ReactNode;
}

export function IngredientsWrapper({ children }: IngredientsWrapperProps) {
  return (
    <AuthenticatedOnly 
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      {children}
    </AuthenticatedOnly>
  );
}
