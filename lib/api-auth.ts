import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { UserRole, AuthUser, UserProfile } from './auth-types';

export async function withAuth(
  request: NextRequest,
  handler: (request: NextRequest, user: AuthUser, profile: UserProfile) => Promise<NextResponse>
) {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(_cookiesToSet) {
            // Not needed for API routes
          },
        },
      }
    );

    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 403 }
      );
    }

    return handler(request, user, profile);
  } catch (error) {
    console.error('Auth middleware error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function withRole(
  request: NextRequest,
  roles: UserRole | UserRole[],
  handler: (request: NextRequest, user: AuthUser, profile: UserProfile) => Promise<NextResponse>
) {
  return withAuth(request, async (req, user, profile) => {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    
    if (!roleArray.includes(profile.role)) {
      return NextResponse.json(
        { error: `Access denied. Required role: ${roleArray.join(' or ')}` },
        { status: 403 }
      );
    }
    
    return handler(req, user, profile);
  });
}

export async function withAdmin(
  request: NextRequest,
  handler: (request: NextRequest, user: AuthUser, profile: UserProfile) => Promise<NextResponse>
) {
  return withRole(request, 'admin', handler);
}

export async function withStaffOrAdmin(
  request: NextRequest,
  handler: (request: NextRequest, user: AuthUser, profile: UserProfile) => Promise<NextResponse>
) {
  return withRole(request, ['staff', 'admin'], handler);
}
