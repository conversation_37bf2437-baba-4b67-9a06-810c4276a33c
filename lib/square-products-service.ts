import { supabaseAdmin } from './supabase';

export interface SquareProduct {
  id: string;
  name: string;
  category?: string;
  price: number;
  description?: string;
  imageUrl?: string;
  isActive: boolean;
  variations?: SquareProductVariation[];
}

export interface SquareProductVariation {
  id: string;
  name: string;
  price: number;
  sku?: string;
}

// Function to determine product category based on name
function categorizeProduct(name: string): string {
  const nameLower = name.toLowerCase();
  
  if (nameLower.includes('rice') || nameLower.includes('fried rice')) {
    return 'Rice';
  } else if (nameLower.includes('noodle') || nameLower.includes('pad thai') || nameLower.includes('lo mein') || nameLower.includes('ramen')) {
    return 'Noodles';
  } else if (nameLower.includes('coffee') || nameLower.includes('tea') || nameLower.includes('juice') || nameLower.includes('soda') || nameLower.includes('water') || nameLower.includes('drink')) {
    return 'Drinks';
  } else if (nameLower.includes('appetizer') || nameLower.includes('spring roll') || nameLower.includes('dumpling') || nameLower.includes('starter')) {
    return 'Appetizer';
  } else if (nameLower.includes('dessert') || nameLower.includes('cake') || nameLower.includes('ice cream') || nameLower.includes('sweet')) {
    return 'Dessert';
  } else if (nameLower.includes('side') || nameLower.includes('sauce') || nameLower.includes('extra')) {
    return 'Side';
  } else {
    return 'Entree'; // Default category for main dishes
  }
}

// Fetch products from Square catalog using direct API call
export async function fetchProductsFromSquare(): Promise<SquareProduct[]> {
  try {
    console.log('Fetching products from Square catalog...');

    const accessToken = process.env.SQUARE_ACCESS_TOKEN;
    if (!accessToken) {
      throw new Error('Square access token not configured');
    }

    const response = await fetch('https://connect.squareup.com/v2/catalog/list', {
      method: 'GET',
      headers: {
        'Square-Version': '2025-06-18',
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Square API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const catalogObjects = data.objects || [];

    console.log(`Found ${catalogObjects.length} catalog objects from Square`);

    const products: SquareProduct[] = [];

    for (const obj of catalogObjects) {
      // Only process ITEM objects (products)
      if (obj.type !== 'ITEM') continue;

      const itemData = obj.item_data;
      if (!itemData) continue;

      // Skip if item is deleted
      if (obj.is_deleted) continue;

      const variations = itemData.variations || [];
      const productVariations: SquareProductVariation[] = [];

      // Process variations (different sizes, options, etc.)
      for (const variation of variations) {
        if (variation.type !== 'ITEM_VARIATION') continue;

        const variationData = variation.item_variation_data;
        if (!variationData) continue;

        // Convert Square money format (cents) to dollars
        const priceAmount = variationData.price_money?.amount || 0;
        const price = priceAmount / 100; // Convert cents to dollars

        productVariations.push({
          id: variation.id,
          name: variationData.name || 'Default',
          price: price,
          sku: variationData.sku,
        });
      }

      // Use the first variation's price as the main price
      const mainPrice = productVariations.length > 0 ? productVariations[0].price : 0;

      const product: SquareProduct = {
        id: obj.id,
        name: itemData.name || 'Unknown Product',
        category: categorizeProduct(itemData.name || ''),
        price: mainPrice,
        description: itemData.description,
        imageUrl: itemData.image_url,
        isActive: !obj.is_deleted,
        variations: productVariations,
      };

      products.push(product);
      console.log(`Processed product: ${product.name} - $${product.price} (${product.category})`);
    }

    console.log(`Successfully processed ${products.length} products from Square`);
    return products;

  } catch (error) {
    console.error('Error fetching products from Square:', error);
    throw error;
  }
}

// Cache Square products in Supabase
export async function cacheSquareProducts(products: SquareProduct[]): Promise<void> {
  if (!supabaseAdmin) {
    console.warn('Supabase admin client not available, skipping product caching');
    return;
  }
  
  try {
    console.log(`Caching ${products.length} products in Supabase...`);
    
    for (const product of products) {
      // Cache in cached_products table (Square format)
      const { error: cacheError } = await supabaseAdmin
        .from('cached_products')
        .upsert({
          square_catalog_id: product.id,
          name: product.name,
          category: product.category,
          price: Math.round(product.price * 100), // Store as cents
          description: product.description,
          image_url: product.imageUrl,
          is_active: product.isActive,
          cached_at: new Date().toISOString(),
        }, {
          onConflict: 'square_catalog_id'
        });
      
      if (cacheError) {
        console.error(`Error caching product ${product.name}:`, cacheError);
        continue;
      }
      
      // Also add to products table (inventory format) if not exists
      const { error: productError } = await supabaseAdmin
        .from('products')
        .upsert({
          name: product.name,
          category: product.category || 'Entree',
          price: product.price,
          cost: 0, // Will be set manually later
          stock: 0, // Will be set manually later
          min_stock: 5, // Default minimum stock
          description: product.description,
          image_url: product.imageUrl,
          status: product.isActive ? 'active' : 'inactive',
          square_catalog_id: product.id,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'square_catalog_id'
        });
      
      if (productError) {
        console.error(`Error adding product to inventory ${product.name}:`, productError);
      }
    }
    
    console.log('Successfully cached all products in Supabase');
    
  } catch (error) {
    console.error('Error caching products in Supabase:', error);
    throw error;
  }
}

// Get products from cache or fetch from Square
export async function getProducts(forceRefresh: boolean = false): Promise<SquareProduct[]> {
  if (!forceRefresh && supabaseAdmin) {
    try {
      // Try to get from cache first
      const { data: cachedProducts, error } = await supabaseAdmin
        .from('cached_products')
        .select('*')
        .eq('is_active', true)
        .order('name');
      
      if (!error && cachedProducts && cachedProducts.length > 0) {
        console.log(`Found ${cachedProducts.length} cached products`);
        
        return cachedProducts.map(product => ({
          id: product.square_catalog_id,
          name: product.name,
          category: product.category,
          price: product.price / 100, // Convert from cents to dollars
          description: product.description,
          imageUrl: product.image_url,
          isActive: product.is_active,
        }));
      }
    } catch (error) {
      console.log('Error fetching cached products, will fetch from Square:', error);
    }
  }
  
  // Fetch from Square and cache
  const products = await fetchProductsFromSquare();
  await cacheSquareProducts(products);
  
  return products;
}

// Sync products from Square (for API endpoint)
export async function syncProductsFromSquare(): Promise<{ success: boolean; count: number; message: string }> {
  try {
    console.log('Starting product sync from Square...');
    
    const products = await fetchProductsFromSquare();
    await cacheSquareProducts(products);
    
    return {
      success: true,
      count: products.length,
      message: `Successfully synced ${products.length} products from Square`
    };
    
  } catch (error) {
    console.error('Product sync failed:', error);
    return {
      success: false,
      count: 0,
      message: `Product sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
