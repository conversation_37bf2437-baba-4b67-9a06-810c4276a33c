import { SalesReportData } from './square-client';

// Mock sales data for demonstration when Square API is not configured
export const mockSalesData: SalesReportData = {
  totalSales: 8750.25,
  totalOrders: 312,
  averageOrderValue: 28.04,
  totalTax: 875.03,
  totalTips: 1250.15,
  topProducts: [
    { name: 'Chicken Fried Rice', quantity: 45, revenue: 629.55 },
    { name: 'Pad Thai', quantity: 38, revenue: 569.62 },
    { name: '<PERSON><PERSON><PERSON>', quantity: 32, revenue: 543.68 },
    { name: 'Grilled Salmon', quantity: 28, revenue: 643.72 },
    { name: 'Iced Coffee', quantity: 67, revenue: 267.33 },
    { name: 'Chicken Teriyaki', quantity: 25, revenue: 474.75 },
    { name: 'Vegetable Fried Rice', quantity: 31, revenue: 371.69 },
    { name: 'Fresh Orange Juice', quantity: 42, revenue: 209.58 },
    { name: 'Singapore Noodles', quantity: 22, revenue: 351.78 },
    { name: 'Green Tea', quantity: 38, revenue: 113.62 },
  ],
  salesByDay: [
    { date: '2024-01-07', sales: 1250.75, orders: 45 },
    { date: '2024-01-08', sales: 1456.80, orders: 52 },
    { date: '2024-01-09', sales: 1123.45, orders: 38 },
    { date: '2024-01-10', sales: 1678.90, orders: 58 },
    { date: '2024-01-11', sales: 1345.25, orders: 47 },
    { date: '2024-01-12', sales: 1567.80, orders: 55 },
    { date: '2024-01-13', sales: 1789.45, orders: 62 },
  ],
};

// Function to generate mock data with static dates (to avoid Next.js warnings)
export function generateMockSalesData(_days: number = 7): SalesReportData {
  // Use the static mock data which already has proper dates
  return mockSalesData;
}
