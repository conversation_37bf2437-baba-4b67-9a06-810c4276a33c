/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { Product, Ingredient, DashboardStats, LowStockAlert, ActivityLog } from './types';

// Products Data
export const allProducts: Product[] = [
  // Rice Products
  {
    id: 1,
    name: 'Chicken Fried Rice',
    category: 'Rice',
    price: 13.99,
    cost: 4.80,
    stock: 18,
    minStock: 5,
    ingredients: ['Jasmine Rice', 'Chicken Breast', 'Eggs', 'Soy Sauce', 'Green Onions', 'Carrots'],
    description: 'Classic fried rice with tender chicken and fresh vegetables',
    status: 'active',
  },
  {
    id: 2,
    name: 'Vegetable Fried Rice',
    category: 'Rice',
    price: 11.99,
    cost: 3.50,
    stock: 22,
    minStock: 5,
    ingredients: ['Jasmine Rice', 'Mixed Vegetables', 'Eggs', 'Soy Sauce', 'Garlic'],
    description: 'Healthy vegetarian fried rice with seasonal vegetables',
    status: 'active',
  },
  {
    id: 3,
    name: 'Beef Fried Rice',
    category: 'Rice',
    price: 15.99,
    cost: 5.20,
    stock: 3,
    minStock: 5,
    ingredients: ['Jasmine Rice', 'Beef Strips', 'Eggs', 'Soy Sauce', 'Bean Sprouts'],
    description: 'Savory fried rice with marinated beef strips',
    status: 'active',
  },
  {
    id: 4,
    name: 'Shrimp Fried Rice',
    category: 'Rice',
    price: 16.99,
    cost: 6.00,
    stock: 12,
    minStock: 5,
    ingredients: ['Jasmine Rice', 'Shrimp', 'Eggs', 'Soy Sauce', 'Peas', 'Carrots'],
    description: 'Premium fried rice with fresh shrimp',
    status: 'active',
  },
  {
    id: 5,
    name: 'Thai Basil Rice',
    category: 'Rice',
    price: 14.99,
    cost: 4.50,
    stock: 8,
    minStock: 5,
    ingredients: ['Jasmine Rice', 'Thai Basil', 'Chili', 'Garlic', 'Fish Sauce'],
    description: 'Aromatic Thai-style rice with fresh basil',
    status: 'active',
  },

  // Noodle Products
  {
    id: 6,
    name: 'Pad Thai',
    category: 'Noodles',
    price: 14.99,
    cost: 5.20,
    stock: 15,
    minStock: 5,
    ingredients: ['Rice Noodles', 'Shrimp', 'Bean Sprouts', 'Tamarind', 'Fish Sauce', 'Peanuts'],
    description: 'Traditional Thai stir-fried noodles with shrimp',
    status: 'active',
  },
  {
    id: 7,
    name: 'Lo Mein',
    category: 'Noodles',
    price: 12.99,
    cost: 4.50,
    stock: 20,
    minStock: 5,
    ingredients: ['Egg Noodles', 'Vegetables', 'Soy Sauce', 'Sesame Oil'],
    description: 'Chinese soft noodles with mixed vegetables',
    status: 'active',
  },
  {
    id: 8,
    name: 'Beef Pho',
    category: 'Noodles',
    price: 16.99,
    cost: 6.80,
    stock: 2,
    minStock: 5,
    ingredients: ['Rice Noodles', 'Beef Broth', 'Beef Slices', 'Herbs', 'Onions'],
    description: 'Vietnamese noodle soup with tender beef',
    status: 'active',
  },
  {
    id: 9,
    name: 'Chicken Ramen',
    category: 'Noodles',
    price: 13.99,
    cost: 4.20,
    stock: 18,
    minStock: 5,
    ingredients: ['Ramen Noodles', 'Chicken Broth', 'Chicken', 'Eggs', 'Green Onions'],
    description: 'Japanese ramen with rich chicken broth',
    status: 'active',
  },
  {
    id: 10,
    name: 'Singapore Noodles',
    category: 'Noodles',
    price: 15.99,
    cost: 5.50,
    stock: 12,
    minStock: 5,
    ingredients: ['Rice Vermicelli', 'Curry Powder', 'Shrimp', 'Char Siu', 'Bell Peppers'],
    description: 'Curry-flavored rice noodles with mixed proteins',
    status: 'active',
  },
  {
    id: 11,
    name: 'Udon Soup',
    category: 'Noodles',
    price: 11.99,
    cost: 3.80,
    stock: 25,
    minStock: 5,
    ingredients: ['Udon Noodles', 'Dashi Broth', 'Tempura', 'Seaweed', 'Green Onions'],
    description: 'Japanese thick noodle soup with tempura',
    status: 'active',
  },

  // Entree Products
  {
    id: 12,
    name: 'Grilled Salmon',
    category: 'Entree',
    price: 22.99,
    cost: 9.50,
    stock: 8,
    minStock: 5,
    ingredients: ['Salmon Fillet', 'Lemon', 'Herbs', 'Olive Oil', 'Asparagus'],
    description: 'Fresh grilled salmon with seasonal vegetables',
    status: 'active',
  },
  {
    id: 13,
    name: 'Beef Steak',
    category: 'Entree',
    price: 28.99,
    cost: 12.00,
    stock: 6,
    minStock: 5,
    ingredients: ['Ribeye Steak', 'Garlic', 'Butter', 'Rosemary', 'Mashed Potatoes'],
    description: 'Premium ribeye steak with garlic butter',
    status: 'active',
  },
  {
    id: 14,
    name: 'Chicken Teriyaki',
    category: 'Entree',
    price: 18.99,
    cost: 6.80,
    stock: 15,
    minStock: 5,
    ingredients: ['Chicken Thigh', 'Teriyaki Sauce', 'Rice', 'Broccoli'],
    description: 'Glazed chicken thigh with teriyaki sauce',
    status: 'active',
  },
  {
    id: 15,
    name: 'Pork Ribs',
    category: 'Entree',
    price: 24.99,
    cost: 8.50,
    stock: 4,
    minStock: 5,
    ingredients: ['Pork Ribs', 'BBQ Sauce', 'Coleslaw', 'Corn'],
    description: 'Slow-cooked BBQ pork ribs with sides',
    status: 'active',
  },
  {
    id: 16,
    name: 'Vegetarian Curry',
    category: 'Entree',
    price: 16.99,
    cost: 5.20,
    stock: 12,
    minStock: 5,
    ingredients: ['Mixed Vegetables', 'Coconut Milk', 'Curry Spices', 'Basmati Rice'],
    description: 'Rich vegetarian curry with coconut milk',
    status: 'active',
  },
  {
    id: 17,
    name: 'Fish and Chips',
    category: 'Entree',
    price: 19.99,
    cost: 7.20,
    stock: 10,
    minStock: 5,
    ingredients: ['White Fish', 'Batter', 'Potatoes', 'Tartar Sauce', 'Mushy Peas'],
    description: 'Classic beer-battered fish with chips',
    status: 'active',
  },

  // Drink Products
  {
    id: 18,
    name: 'Fresh Orange Juice',
    category: 'Drinks',
    price: 4.99,
    cost: 1.50,
    stock: 30,
    minStock: 10,
    ingredients: ['Fresh Oranges', 'Ice'],
    description: 'Freshly squeezed orange juice',
    status: 'active',
  },
  {
    id: 19,
    name: 'Iced Coffee',
    category: 'Drinks',
    price: 3.99,
    cost: 1.20,
    stock: 50,
    minStock: 15,
    ingredients: ['Coffee Beans', 'Ice', 'Milk', 'Sugar'],
    description: 'Cold brew coffee with milk',
    status: 'active',
  },
  {
    id: 20,
    name: 'Green Tea',
    category: 'Drinks',
    price: 2.99,
    cost: 0.80,
    stock: 40,
    minStock: 10,
    ingredients: ['Green Tea Leaves', 'Hot Water'],
    description: 'Traditional green tea',
    status: 'active',
  },
  {
    id: 21,
    name: 'Mango Smoothie',
    category: 'Drinks',
    price: 6.99,
    cost: 2.50,
    stock: 8,
    minStock: 10,
    ingredients: ['Mango', 'Yogurt', 'Honey', 'Ice'],
    description: 'Creamy mango smoothie with yogurt',
    status: 'active',
  },
  {
    id: 22,
    name: 'Lemonade',
    category: 'Drinks',
    price: 3.49,
    cost: 1.00,
    stock: 25,
    minStock: 10,
    ingredients: ['Lemons', 'Sugar', 'Water', 'Ice'],
    description: 'Fresh homemade lemonade',
    status: 'active',
  },
  {
    id: 23,
    name: 'Thai Iced Tea',
    category: 'Drinks',
    price: 4.49,
    cost: 1.80,
    stock: 15,
    minStock: 10,
    ingredients: ['Thai Tea', 'Condensed Milk', 'Ice'],
    description: 'Sweet Thai tea with condensed milk',
    status: 'active',
  },
  {
    id: 24,
    name: 'Coconut Water',
    category: 'Drinks',
    price: 3.99,
    cost: 1.50,
    stock: 20,
    minStock: 10,
    ingredients: ['Fresh Coconut'],
    description: 'Natural coconut water',
    status: 'active',
  },
  {
    id: 25,
    name: 'Hot Chocolate',
    category: 'Drinks',
    price: 4.99,
    cost: 1.80,
    stock: 5,
    minStock: 10,
    ingredients: ['Cocoa Powder', 'Milk', 'Sugar', 'Whipped Cream'],
    description: 'Rich hot chocolate with whipped cream',
    status: 'active',
  },
];

// Helper functions to get filtered data
export const getProductsByCategory = (category: string): Product[] => {
  if (category === 'All' || category === '') return allProducts;
  return allProducts.filter(product => product.category === category);
};

export const getIngredientsByCategory = (category: string): Ingredient[] => {
  if (category === 'All' || category === '') return allIngredients;
  return allIngredients.filter(ingredient => ingredient.category === category);
};

// Ingredients Data
export const allIngredients: Ingredient[] = [
  // Meat Ingredients
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: 'Ground Beef',
    category: 'Meat',
    unit: 'lbs',
    stock: 45,
    minStock: 20,
    maxStock: 100,
    costPerUnit: 6.99,
    supplier: 'Local Butcher Co.',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-01-20',
    location: 'Freezer A1',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: 'Chicken Breast',
    category: 'Meat',
    unit: 'lbs',
    stock: 2,
    minStock: 10,
    maxStock: 50,
    costPerUnit: 8.99,
    supplier: 'Local Butcher Co.',
    lastRestocked: '2024-01-08',
    expiryDate: '2024-01-18',
    location: 'Freezer A2',
  },
  {
    id: '3',
    name: 'Salmon Fillet',
    category: 'Meat',
    unit: 'lbs',
    stock: 12,
    minStock: 8,
    maxStock: 30,
    costPerUnit: 15.99,
    supplier: 'Fresh Fish Market',
    lastRestocked: '2024-01-11',
    expiryDate: '2024-01-16',
    location: 'Freezer B1',
  },
  {
    id: 4,
    name: 'Pork Ribs',
    category: 'Meat',
    unit: 'lbs',
    stock: 8,
    minStock: 5,
    maxStock: 25,
    costPerUnit: 9.99,
    supplier: 'Local Butcher Co.',
    lastRestocked: '2024-01-09',
    expiryDate: '2024-01-19',
    location: 'Freezer A3',
  },
  {
    id: 5,
    name: 'Shrimp',
    category: 'Meat',
    unit: 'lbs',
    stock: 6,
    minStock: 8,
    maxStock: 20,
    costPerUnit: 12.99,
    supplier: 'Seafood Express',
    lastRestocked: '2024-01-12',
    expiryDate: '2024-01-17',
    location: 'Freezer B2',
  },
  {
    id: 6,
    name: 'Chicken Thigh',
    category: 'Meat',
    unit: 'lbs',
    stock: 18,
    minStock: 10,
    maxStock: 40,
    costPerUnit: 6.99,
    supplier: 'Local Butcher Co.',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-01-20',
    location: 'Freezer A4',
  },

  // Dry Ingredients
  {
    id: 7,
    name: 'Jasmine Rice',
    category: 'Dry',
    unit: 'lbs',
    stock: 50,
    minStock: 20,
    maxStock: 100,
    costPerUnit: 2.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-05',
    expiryDate: '2024-12-31',
    location: 'Dry Storage A',
  },
  {
    id: 8,
    name: 'Rice Noodles',
    category: 'Dry',
    unit: 'lbs',
    stock: 15,
    minStock: 10,
    maxStock: 50,
    costPerUnit: 3.49,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-08',
    expiryDate: '2024-08-15',
    location: 'Dry Storage B',
  },
  {
    id: 9,
    name: 'All-Purpose Flour',
    category: 'Dry',
    unit: 'lbs',
    stock: 25,
    minStock: 15,
    maxStock: 60,
    costPerUnit: 1.99,
    supplier: 'Baking Supplies Inc.',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-06-30',
    location: 'Dry Storage C',
  },
  {
    id: 10,
    name: 'Coffee Beans',
    category: 'Dry',
    unit: 'lbs',
    stock: 8,
    minStock: 5,
    maxStock: 25,
    costPerUnit: 12.99,
    supplier: 'Premium Coffee Co.',
    lastRestocked: '2024-01-05',
    expiryDate: '2024-03-05',
    location: 'Dry Storage D',
  },
  {
    id: 11,
    name: 'Sugar',
    category: 'Dry',
    unit: 'lbs',
    stock: 30,
    minStock: 20,
    maxStock: 80,
    costPerUnit: 1.49,
    supplier: 'Sweet Supply Co.',
    lastRestocked: '2024-01-12',
    expiryDate: '2025-01-12',
    location: 'Dry Storage E',
  },
  {
    id: 12,
    name: 'Black Pepper',
    category: 'Dry',
    unit: 'oz',
    stock: 12,
    minStock: 8,
    maxStock: 30,
    costPerUnit: 0.99,
    supplier: 'Spice World',
    lastRestocked: '2024-01-07',
    expiryDate: '2024-07-07',
    location: 'Spice Rack A',
  },

  // Liquid Ingredients
  {
    id: 13,
    name: 'Soy Sauce',
    category: 'Liquid',
    unit: 'bottles',
    stock: 8,
    minStock: 5,
    maxStock: 20,
    costPerUnit: 3.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-08',
    expiryDate: '2025-01-08',
    location: 'Pantry A',
  },
  {
    id: 14,
    name: 'Olive Oil',
    category: 'Liquid',
    unit: 'bottles',
    stock: 12,
    minStock: 8,
    maxStock: 25,
    costPerUnit: 8.99,
    supplier: 'Mediterranean Foods',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-12-10',
    location: 'Pantry B',
  },
  {
    id: 15,
    name: 'Coconut Milk',
    category: 'Liquid',
    unit: 'cans',
    stock: 15,
    minStock: 10,
    maxStock: 30,
    costPerUnit: 2.49,
    supplier: 'Tropical Imports',
    lastRestocked: '2024-01-09',
    expiryDate: '2024-08-09',
    location: 'Pantry C',
  },
  {
    id: 16,
    name: 'Fish Sauce',
    category: 'Liquid',
    unit: 'bottles',
    stock: 4,
    minStock: 5,
    maxStock: 15,
    costPerUnit: 4.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-05',
    expiryDate: '2025-06-05',
    location: 'Pantry A',
  },
  {
    id: 17,
    name: 'Sesame Oil',
    category: 'Liquid',
    unit: 'bottles',
    stock: 6,
    minStock: 4,
    maxStock: 12,
    costPerUnit: 6.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-07',
    expiryDate: '2024-10-07',
    location: 'Pantry A',
  },
  {
    id: 18,
    name: 'Vegetable Oil',
    category: 'Liquid',
    unit: 'gallons',
    stock: 3,
    minStock: 2,
    maxStock: 8,
    costPerUnit: 12.99,
    supplier: 'Cooking Supplies Co.',
    lastRestocked: '2024-01-11',
    expiryDate: '2024-11-11',
    location: 'Storage Room',
  },

  // Bag Ingredients
  {
    id: 19,
    name: 'Onions',
    category: 'Bag',
    unit: 'bags',
    stock: 8,
    minStock: 5,
    maxStock: 20,
    costPerUnit: 4.99,
    supplier: 'Fresh Farm Produce',
    lastRestocked: '2024-01-11',
    expiryDate: '2024-01-25',
    location: 'Produce Storage',
  },
  {
    id: 20,
    name: 'Potatoes',
    category: 'Bag',
    unit: 'bags',
    stock: 12,
    minStock: 8,
    maxStock: 25,
    costPerUnit: 6.99,
    supplier: 'Fresh Farm Produce',
    lastRestocked: '2024-01-10',
    expiryDate: '2024-02-10',
    location: 'Produce Storage',
  },
  {
    id: 21,
    name: 'Carrots',
    category: 'Bag',
    unit: 'bags',
    stock: 6,
    minStock: 5,
    maxStock: 15,
    costPerUnit: 3.99,
    supplier: 'Fresh Farm Produce',
    lastRestocked: '2024-01-09',
    expiryDate: '2024-01-23',
    location: 'Produce Storage',
  },
  {
    id: 22,
    name: 'Frozen Peas',
    category: 'Bag',
    unit: 'bags',
    stock: 10,
    minStock: 6,
    maxStock: 20,
    costPerUnit: 2.99,
    supplier: 'Frozen Foods Co.',
    lastRestocked: '2024-01-08',
    expiryDate: '2024-07-08',
    location: 'Freezer C1',
  },
  {
    id: 23,
    name: 'Mixed Vegetables',
    category: 'Bag',
    unit: 'bags',
    stock: 4,
    minStock: 6,
    maxStock: 18,
    costPerUnit: 4.49,
    supplier: 'Frozen Foods Co.',
    lastRestocked: '2024-01-07',
    expiryDate: '2024-06-07',
    location: 'Freezer C2',
  },
  {
    id: 24,
    name: 'Bean Sprouts',
    category: 'Bag',
    unit: 'bags',
    stock: 3,
    minStock: 5,
    maxStock: 12,
    costPerUnit: 1.99,
    supplier: 'Asian Grocery Co.',
    lastRestocked: '2024-01-12',
    expiryDate: '2024-01-17',
    location: 'Cooler D1',
  },
];

// Dashboard Stats
export const dashboardStats: DashboardStats = {
  totalProducts: allProducts.length,
  lowStockProducts: allProducts.filter(p => p.stock <= p.minStock).length,
  totalIngredients: allIngredients.length,
  lowStockIngredients: allIngredients.filter(i => i.stock <= i.minStock).length,
  todaysSales: 1250.75,
  weeklyRevenue: 8750.25,
  monthlyRevenue: 35420.80,
  totalInventoryValue: allIngredients.reduce((sum, i) => sum + (i.stock * i.costPerUnit), 0),
};

// Low Stock Alerts
export const lowStockAlerts: LowStockAlert[] = [
  {
    id: 1,
    name: 'Chicken Breast',
    type: 'ingredient',
    stock: 2,
    unit: 'lbs',
    minStock: 10,
    severity: 'critical',
  },
  {
    id: 2,
    name: 'Bean Sprouts',
    type: 'ingredient',
    stock: 3,
    unit: 'bags',
    minStock: 5,
    severity: 'low',
  },
  {
    id: 3,
    name: 'Beef Fried Rice',
    type: 'product',
    stock: 3,
    unit: 'servings',
    minStock: 5,
    severity: 'low',
  },
  {
    id: 4,
    name: 'Fish Sauce',
    type: 'ingredient',
    stock: 4,
    unit: 'bottles',
    minStock: 5,
    severity: 'low',
  },
  {
    id: 5,
    name: 'Beef Pho',
    type: 'product',
    stock: 2,
    unit: 'servings',
    minStock: 5,
    severity: 'critical',
  },
];

// Recent Activity
export const recentActivity: ActivityLog[] = [
  {
    id: 1,
    action: 'Stock Updated',
    item: 'Ground Beef',
    itemType: 'ingredient',
    quantity: 20,
    user: 'John Doe',
    timestamp: '2024-01-13T12:30:00.000Z', // 2 hours ago
    details: 'Restocked from Local Butcher Co.',
  },
  {
    id: 2,
    action: 'New Product Added',
    item: 'Veggie Burger',
    itemType: 'product',
    user: 'Jane Smith',
    timestamp: '2024-01-13T10:30:00.000Z', // 4 hours ago
    details: 'Added to Entree category',
  },
  {
    id: 3,
    action: 'Low Stock Alert',
    item: 'Chicken Breast',
    itemType: 'ingredient',
    user: 'System',
    timestamp: '2024-01-13T08:30:00.000Z', // 6 hours ago
    details: 'Stock level below minimum threshold',
  },
  {
    id: 4,
    action: 'Price Updated',
    item: 'Pad Thai',
    itemType: 'product',
    user: 'Mike Johnson',
    timestamp: '2024-01-13T06:30:00.000Z', // 8 hours ago
    details: 'Price increased from $13.99 to $14.99',
  },
  {
    id: 5,
    action: 'Ingredient Expired',
    item: 'Lettuce',
    itemType: 'ingredient',
    quantity: 5,
    user: 'System',
    timestamp: '2024-01-13T02:30:00.000Z', // 12 hours ago
    details: 'Removed expired items from inventory',
  },
];
