// Client-side service that calls API routes
// All database operations are handled server-side

export interface Supplier {
  id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class SuppliersService {
  // CRUD Operations for Suppliers
  async getAllSuppliers(): Promise<Supplier[]> {
    try {
      console.log('Client: Fetching suppliers from API');
      const response = await fetch('/api/suppliers');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        // Return default suppliers as fallback
        return this.getDefaultSuppliers();
      }

      const data = await response.json();
      console.log('Client: Received suppliers:', data.length);
      return data;
    } catch (error) {
      console.error('Client error fetching suppliers:', error);
      return this.getDefaultSuppliers();
    }
  }

  private getDefaultSuppliers(): Supplier[] {
    return [
      {
        id: '1',
        name: 'Fresh Farm Produce',
        contact_person: '<PERSON>',
        email: '<EMAIL>',
        phone: '(*************',
        address: '123 Farm Road, Valley City',
        notes: 'Reliable supplier for fresh ingredients',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Asian Grocery Co.',
        contact_person: 'Lisa Chen',
        email: '<EMAIL>',
        phone: '(*************',
        address: '456 Market Street, Chinatown',
        notes: 'Best prices for Asian ingredients',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Wholesale Meats Co.',
        contact_person: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '(*************',
        address: '789 Industrial Blvd, Meat District',
        notes: 'High quality meats, bulk discounts',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '4',
        name: 'Restaurant Supply Plus',
        contact_person: 'Sarah Davis',
        email: '<EMAIL>',
        phone: '(*************',
        address: '321 Supply Avenue, Business Park',
        notes: 'One-stop shop for restaurant supplies',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '5',
        name: 'Spice World',
        contact_person: 'David Kumar',
        email: '<EMAIL>',
        phone: '(*************',
        address: '654 Spice Lane, Flavor Town',
        notes: 'Premium spices and seasonings',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  async getSupplierById(id: string): Promise<Supplier | null> {
    try {
      const response = await fetch(`/api/suppliers/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Client error fetching supplier:', error);
      return null;
    }
  }

  async createSupplier(supplier: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>): Promise<Supplier> {
    try {
      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(supplier),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create supplier');
      }

      return await response.json();
    } catch (error) {
      console.error('Client error creating supplier:', error);
      throw error;
    }
  }

  async updateSupplier(id: string, updates: Partial<Supplier>): Promise<Supplier> {
    try {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update supplier');
      }

      return await response.json();
    } catch (error) {
      console.error('Client error updating supplier:', error);
      throw error;
    }
  }

  async deleteSupplier(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/suppliers/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete supplier');
      }
    } catch (error) {
      console.error('Client error deleting supplier:', error);
      throw error;
    }
  }

  async searchSuppliers(query: string): Promise<Supplier[]> {
    try {
      const response = await fetch(`/api/suppliers?search=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error('Failed to search suppliers');
      }
      return await response.json();
    } catch (error) {
      console.error('Error searching suppliers:', error);
      return [];
    }
  }

  // Get supplier names for dropdown
  async getSupplierNames(): Promise<string[]> {
    const suppliers = await this.getAllSuppliers();
    return suppliers.map(supplier => supplier.name);
  }
}

export const suppliersService = new SuppliersService();
