// Client-side service that calls API routes
// All database operations are handled server-side

export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  cost: number;
  description?: string;
  image_url?: string;
  square_catalog_id?: string;
  ingredients?: ProductIngredient[];
  created_at: string;
  updated_at: string;
}

export interface ProductIngredient {
  ingredient_id: string;
  ingredient_name: string;
  quantity: number;
  unit: string;
}

class ProductsService {
  // CRUD Operations for Products
  async getAllProducts(): Promise<Product[]> {
    try {
      console.log('Client: Fetching products from API');
      const response = await fetch('/api/products');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        return [];
      }

      const data = await response.json();
      console.log('Client: Received products:', data.length);
      return data;
    } catch (error) {
      console.error('Client error fetching products:', error);
      return [];
    }
  }

  async getProductById(id: string): Promise<Product | null> {
    try {
      const response = await fetch(`/api/products/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        return null;
      }

      const product = await response.json();

      // Load ingredients for this product
      try {
        const ingredientsResponse = await fetch(`/api/products/${id}/ingredients`);
        if (ingredientsResponse.ok) {
          const ingredients = await ingredientsResponse.json();
          product.ingredients = ingredients;
        }
      } catch (ingredientsError) {
        console.error('Error loading product ingredients:', ingredientsError);
        product.ingredients = [];
      }

      return product;
    } catch (error) {
      console.error('Client error fetching product:', error);
      return null;
    }
  }

  async createProduct(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> {
    try {
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      return await response.json();
    } catch (error) {
      console.error('Client error creating product:', error);
      throw error;
    }
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<Product> {
    try {
      console.log('Client: Updating product via API:', { id, updates });

      const response = await fetch(`/api/products/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update product');
      }

      const data = await response.json();
      console.log('Client: Updated product:', data);
      return data;
    } catch (error) {
      console.error('Client error updating product:', error);
      throw error;
    }
  }

  async deleteProduct(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Client error deleting product:', error);
      throw error;
    }
  }

  // Search products
  async searchProducts(query: string): Promise<Product[]> {
    try {
      const allProducts = await this.getAllProducts();
      return allProducts.filter(product => 
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(query.toLowerCase()))
      );
    } catch (error) {
      console.error('Client error searching products:', error);
      return [];
    }
  }

  // Get products by category
  async getProductsByCategory(category: string): Promise<Product[]> {
    try {
      const allProducts = await this.getAllProducts();
      return allProducts.filter(product => product.category === category);
    } catch (error) {
      console.error('Client error getting products by category:', error);
      return [];
    }
  }







  // Calculate profit margin
  calculateProfitMargin(product: Product): number {
    if (product.price <= 0 || product.cost <= 0) return 0;
    return ((product.price - product.cost) / product.price) * 100;
  }

  // Calculate average product price
  calculateAveragePrice(products: Product[]): number {
    if (products.length === 0) return 0;
    return products.reduce((total, product) => total + product.price, 0) / products.length;
  }

  // Calculate average product cost
  calculateAverageCost(products: Product[]): number {
    if (products.length === 0) return 0;
    return products.reduce((total, product) => total + product.cost, 0) / products.length;
  }

  // Get product statistics
  getProductStats(products: Product[]): {
    totalProducts: number;
    averagePrice: number;
    averageCost: number;
    averageProfitMargin: number;
  } {
    const averagePrice = this.calculateAveragePrice(products);
    const averageCost = this.calculateAverageCost(products);

    const profitMargins = products
      .filter(p => p.price > 0 && p.cost > 0)
      .map(p => this.calculateProfitMargin(p));

    const averageProfitMargin = profitMargins.length > 0
      ? profitMargins.reduce((sum, margin) => sum + margin, 0) / profitMargins.length
      : 0;

    return {
      totalProducts: products.length,
      averagePrice,
      averageCost,
      averageProfitMargin
    };
  }
}

export const productsService = new ProductsService();
