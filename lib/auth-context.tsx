'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { supabaseClient } from './supabase-client';
import { AuthContextType, UserProfile, AuthUser, UserRole, ROLE_PERMISSIONS, Permission } from './auth-types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Emergency fallback - stop loading after 3 seconds no matter what
  useEffect(() => {
    const emergencyTimeout = setTimeout(() => {
      console.log('🚨 EMERGENCY: Stopping loading state after 3 seconds');
      setLoading(false);
    }, 3000);

    return () => clearTimeout(emergencyTimeout);
  }, []);

  useEffect(() => {
    // Check environment variables first
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing Supabase environment variables');
      console.error('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
      console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseKey ? '✅' : '❌');
      console.log('💡 Create .env.local with your Supabase credentials');
      setLoading(false);
      return;
    }

    // Get initial session with simplified error handling
    const getInitialSession = async () => {
      try {
        console.log('🔍 Checking initial session...');

        const { data: { session }, error } = await supabaseClient.auth.getSession();

        if (error) {
          console.error('❌ Session error:', error);
          return;
        }

        if (session?.user) {
          console.log('✅ User found, loading profile...');
          await loadUserProfile(session.user);
        } else {
          console.log('ℹ️ No active session found');
        }
      } catch (error: unknown) {
        console.error('❌ Error getting initial session:', error);
        console.error('💡 This usually means Supabase credentials are incorrect or missing');
      } finally {
        console.log('✅ Auth initialization complete');
        setLoading(false);
      }
    };

    getInitialSession();

    // Fallback timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.log('⚠️ Auth timeout - stopping loading state');
      setLoading(false);
    }, 10000); // 10 second timeout

    // Listen for auth changes
    const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event);
        clearTimeout(timeoutId); // Clear timeout since we got a response

        if (session?.user) {
          await loadUserProfile(session.user);
        } else {
          setUser(null);
          setProfile(null);
        }
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeoutId);
    };
  }, []);

  const loadUserProfile = async (authUser: User) => {
    try {
      console.log('🔍 Loading profile for user:', authUser.email);

      const { data: profileData, error } = await supabaseClient
        .from('user_profiles')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (error) {
        console.error('❌ Error loading user profile:', error);

        // If profile doesn't exist, create one
        if (error.code === 'PGRST116') {
          console.log('📝 Creating new user profile...');
          const { data: newProfile, error: createError } = await supabaseClient
            .from('user_profiles')
            .insert({
              id: authUser.id,
              email: authUser.email!,
              full_name: authUser.user_metadata?.full_name || authUser.email,
              role: 'viewer' as UserRole
            })
            .select()
            .single();

          if (createError) {
            console.error('❌ Error creating user profile:', createError);
            // Set user without profile
            setUser({
              id: authUser.id,
              email: authUser.email!,
              profile: undefined
            });
            return;
          } else {
            console.log('✅ User profile created successfully');
            setProfile(newProfile);
            setUser({
              id: authUser.id,
              email: authUser.email!,
              profile: newProfile
            });
            return;
          }
        } else {
          // Other database errors - set user without profile
          setUser({
            id: authUser.id,
            email: authUser.email!,
            profile: undefined
          });
          return;
        }
      } else {
        console.log('✅ User profile loaded successfully');
        setProfile(profileData);
        setUser({
          id: authUser.id,
          email: authUser.email!,
          profile: profileData
        });
      }
    } catch (error) {
      console.error('❌ Unexpected error in loadUserProfile:', error);
      // Fallback: set user without profile
      setUser({
        id: authUser.id,
        email: authUser.email!,
        profile: undefined
      });
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabaseClient.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return { error: error.message };
      }

      return {};
    } catch (error) {
      return { error: 'An unexpected error occurred' };
    }
  };

  const signOut = async () => {
    try {
      // Sign out from Supabase
      await supabaseClient.auth.signOut();

      // Clear local state
      setUser(null);
      setProfile(null);

      // Small delay to ensure logout completes, then redirect
      setTimeout(() => {
        window.location.href = '/login';
      }, 100);
    } catch (error) {
      console.error('Error signing out:', error);
      // Even if there's an error, redirect to login page
      window.location.href = '/login';
    }
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!profile) return false;
    
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(profile.role);
  };

  const canAccess = (feature: Permission): boolean => {
    if (!profile) return false;
    
    const userPermissions = ROLE_PERMISSIONS[profile.role];
    return userPermissions.includes(feature);
  };

  const value: AuthContextType = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    hasRole,
    canAccess
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Custom hooks for common auth checks
export function useRequireAuth() {
  const { user, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/login';
    }
  }, [user, loading]);

  return { user, loading };
}

export function useRequireRole(roles: UserRole | UserRole[]) {
  const { hasRole, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !hasRole(roles)) {
      window.location.href = '/unauthorized';
    }
  }, [hasRole, loading, roles]);

  return { hasRole: hasRole(roles), loading };
}
