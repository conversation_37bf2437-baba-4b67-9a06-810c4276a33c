'use client';

import { createContext, useContext, useState } from 'react';
import { AuthContextType, UserProfile, AuthUser, UserRole, Permission, ROLE_PERMISSIONS } from './auth-types';

const FallbackAuthContext = createContext<AuthContextType | undefined>(undefined);

// Fallback auth provider for when Supabase is not available
export function FallbackAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false); // Never loading in fallback mode

  const signIn = async (email: string, password: string) => {
    // Simple demo login - accept any email/password
    console.log('🔧 Using fallback auth - demo mode');
    
    const demoUser: AuthUser = {
      id: 'demo-user-id',
      email: email,
      profile: undefined
    };

    const demoProfile: UserProfile = {
      id: 'demo-user-id',
      email: email,
      full_name: 'Demo User',
      role: 'admin' as UserRole,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setUser(demoUser);
    setProfile(demoProfile);
    
    return {};
  };

  const signOut = async () => {
    setUser(null);
    setProfile(null);
    window.location.href = '/login';
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!profile) return false;
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(profile.role);
  };

  const canAccess = (feature: Permission): boolean => {
    if (!profile) return false;
    const userPermissions = ROLE_PERMISSIONS[profile.role];
    return userPermissions.includes(feature);
  };

  const value: AuthContextType = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    hasRole,
    canAccess
  };

  return (
    <FallbackAuthContext.Provider value={value}>
      {children}
    </FallbackAuthContext.Provider>
  );
}

export function useFallbackAuth() {
  const context = useContext(FallbackAuthContext);
  if (context === undefined) {
    throw new Error('useFallbackAuth must be used within a FallbackAuthProvider');
  }
  return context;
}
