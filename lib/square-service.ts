import { squareApi, SQUARE_LOCATION_ID, formatSquareMoney, type SalesReportData } from './square-client';
import { cacheOrder, getCachedOrders, cacheSalesMetrics } from './supabase';

export async function fetchOrdersFromSquare(startDate: string, endDate: string) {
  // Check permissions first (optional, don't fail if it errors)
  try {
    await squareApi.checkPermissions();
  } catch (error) {
    console.log('Permission check failed, continuing anyway:', error instanceof Error ? error.message : String(error));
  }

  try {
    console.log('Fetching orders from Square API...');
    console.log('Using location ID:', SQUARE_LOCATION_ID);
    console.log('Date range:', startDate, 'to', endDate);

    const requestBody = {
      location_ids: [SQUARE_LOCATION_ID],
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: startDate,
              end_at: endDate,
            },
          },
          state_filter: {
            states: ['COMPLETED'],
          },
        },
        sort: {
          sort_field: 'CREATED_AT',
          sort_order: 'DESC',
        },
      },

      limit: 500, // Maximum allowed by Square API
    };

    const response = await squareApi.searchOrders(requestBody);

    // Handle different response structures
    const orders = response.orders || response.result?.orders || [];

    if (orders.length > 0) {
      console.log(`Found ${orders.length} orders from Square API`);
      // Cache orders in Supabase for faster future access
      for (const order of orders) {
        await cacheOrder(order);
      }

      return orders;
    }

    return [];
  } catch (error) {
    console.error('Error fetching orders from Square:', error);
    
    // Fallback to cached data if Square API fails
    console.log('Falling back to cached data...');
    return await getCachedOrders(startDate, endDate);
  }
}

export async function fetchCatalogFromSquare() {
  try {
    console.log('Fetching catalog from Square API...');
    const response = await squareApi.listCatalog();
    return response.objects || [];
  } catch (error) {
    console.error('Error fetching catalog from Square:', error);
    return [];
  }
}

export async function generateSalesReport(startDate: string, endDate: string): Promise<SalesReportData> {
  const orders = await fetchOrdersFromSquare(startDate, endDate);

  // Initialize report data
  const report: SalesReportData = {
    totalSales: 0,
    totalOrders: 0,
    averageOrderValue: 0,
    totalTax: 0,
    totalTips: 0,
    topProducts: [],
    salesByDay: [],
  };

  const productSales = new Map<string, { name: string; quantity: number; revenue: number }>();
  const dailySales = new Map<string, { sales: number; orders: number }>();

  // Process each order
  for (const order of orders) {
    if (order.state !== 'COMPLETED') continue;

    // Square API uses snake_case field names
    const totalMoney = order.total_money || order.totalMoney;
    const totalTaxMoney = order.total_tax_money || order.totalTaxMoney;

    const orderTotal = formatSquareMoney(totalMoney?.amount || 0);
    const orderTax = formatSquareMoney(totalTaxMoney?.amount || 0);

    // Square API uses snake_case field names
    const lineItems = order.line_items || order.lineItems || [];
    console.log(`Order ${order.id}: ${lineItems.length} line items, total: $${orderTotal}`);
    
    // Update totals
    report.totalSales += orderTotal;
    report.totalOrders += 1;
    report.totalTax += orderTax;

    // Process order date for daily sales - Square API uses snake_case field names
    const orderDate = new Date(order.created_at!).toISOString().split('T')[0];
    const existing = dailySales.get(orderDate) || { sales: 0, orders: 0 };
    dailySales.set(orderDate, {
      sales: existing.sales + orderTotal,
      orders: existing.orders + 1,
    });

    // Process line items for product sales
    if (lineItems.length > 0) {
      for (const item of lineItems) {
        const itemName = item.name || 'Unknown Item';
        const quantity = parseInt(item.quantity || '1');
        const itemTotalMoney = item.total_money || item.totalMoney;
        const revenue = formatSquareMoney(itemTotalMoney?.amount || 0);

        // Track product sales
        const existing = productSales.get(itemName) || { name: itemName, quantity: 0, revenue: 0 };
        productSales.set(itemName, {
          name: itemName,
          quantity: existing.quantity + quantity,
          revenue: existing.revenue + revenue,
        });

      }
    }

    // Process tips from tenders
    if (order.tenders) {
      for (const tender of order.tenders) {
        const tipAmount = formatSquareMoney(tender.tipMoney?.amount || 0);
        report.totalTips += tipAmount;
      }
    }
  }

  // Calculate average order value
  report.averageOrderValue = report.totalOrders > 0 ? report.totalSales / report.totalOrders : 0;

  // Convert maps to arrays and sort
  report.topProducts = Array.from(productSales.values())
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10); // Top 10 products

  report.salesByDay = Array.from(dailySales.entries())
    .map(([date, data]) => ({ date, ...data }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Cache the metrics for faster future access
  try {
    await cacheSalesMetrics({
      date: startDate.split('T')[0],
      location_id: SQUARE_LOCATION_ID,
      total_sales: report.totalSales,
      total_orders: report.totalOrders,
      average_order_value: report.averageOrderValue,
      total_tax: report.totalTax,
      total_tips: report.totalTips,
    });
  } catch (error) {
    console.error('Error caching sales metrics:', error);
  }

  return report;
}

export async function getRecentOrders(limit: number = 10) {
  try {
    console.log('Fetching recent orders from Square API...');
    const response = await squareApi.searchOrders({
      locationIds: [SQUARE_LOCATION_ID],
      query: {
        filter: {
          stateFilter: {
            states: ['COMPLETED'],
          },
        },
        sort: {
          sortField: 'CREATED_AT',
          sortOrder: 'DESC',
        },
      },
      limit,
    });

    return response.result.orders || [];
  } catch (error) {
    console.error('Error fetching recent orders:', error);
    return [];
  }
}
