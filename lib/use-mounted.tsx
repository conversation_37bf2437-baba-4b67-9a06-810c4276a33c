import { useEffect, useState } from 'react';

/**
 * Hook to prevent hydration mismatches by ensuring component is mounted on client
 * @returns boolean indicating if component is mounted on client
 */
export function useMounted() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return mounted;
}

/**
 * Component wrapper to prevent hydration mismatches
 * Shows loading state until component is mounted on client
 */
interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const mounted = useMounted();

  if (!mounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
