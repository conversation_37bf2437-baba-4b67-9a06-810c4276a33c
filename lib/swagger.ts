export const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Inventory Management API',
    description: 'API for managing restaurant inventory, ingredients, and suppliers',
    version: '1.0.0',
    contact: {
      name: 'API Support',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: 'http://localhost:3000/api',
      description: 'Development server'
    }
  ],
  paths: {
    '/ingredients': {
      get: {
        summary: 'Get all ingredients',
        description: 'Retrieve a list of all ingredients in the inventory',
        tags: ['Ingredients'],
        responses: {
          '200': {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Ingredient' }
                }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      },
      post: {
        summary: 'Create new ingredient',
        description: 'Add a new ingredient to the inventory',
        tags: ['Ingredients'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateIngredient' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Ingredient created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Ingredient' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      }
    },
    '/ingredients/{id}': {
      get: {
        summary: 'Get ingredient by ID',
        description: 'Retrieve a specific ingredient by its UUID',
        tags: ['Ingredients'],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'Ingredient UUID',
            schema: {
              type: 'string',
              format: 'uuid'
            }
          }
        ],
        responses: {
          '200': {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Ingredient' }
              }
            }
          },
          '404': {
            description: 'Ingredient not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      },
      put: {
        summary: 'Update ingredient',
        description: 'Update an existing ingredient',
        tags: ['Ingredients'],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'Ingredient UUID',
            schema: {
              type: 'string',
              format: 'uuid'
            }
          }
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateIngredient' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Ingredient updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Ingredient' }
              }
            }
          },
          '404': {
            description: 'Ingredient not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      },
      delete: {
        summary: 'Delete ingredient',
        description: 'Remove an ingredient from the inventory',
        tags: ['Ingredients'],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'Ingredient UUID',
            schema: {
              type: 'string',
              format: 'uuid'
            }
          }
        ],
        responses: {
          '200': {
            description: 'Ingredient deleted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: {
                      type: 'boolean',
                      example: true
                    }
                  }
                }
              }
            }
          },
          '404': {
            description: 'Ingredient not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      }
    },
    '/suppliers': {
      get: {
        summary: 'Get all suppliers',
        description: 'Retrieve a list of all active suppliers',
        tags: ['Suppliers'],
        responses: {
          '200': {
            description: 'Successful response',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Supplier' }
                }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      },
      post: {
        summary: 'Create new supplier',
        description: 'Add a new supplier to the system',
        tags: ['Suppliers'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateSupplier' }
            }
          }
        },
        responses: {
          '200': {
            description: 'Supplier created successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Supplier' }
              }
            }
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' }
              }
            }
          }
        }
      }
    }
  },
  components: {
    schemas: {
      Ingredient: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the ingredient'
          },
          name: {
            type: 'string',
            description: 'Name of the ingredient',
            example: 'Jasmine Rice'
          },
          category: {
            type: 'string',
            description: 'Category of the ingredient',
            enum: ['All', 'Meat', 'Dry', 'Liquid', 'Bag', 'Vegetables', 'Dairy', 'Spices', 'Frozen'],
            example: 'Dry'
          },
          unit: {
            type: 'string',
            description: 'Unit of measurement',
            enum: ['lbs', 'kg', 'oz', 'grams', 'bottles', 'cans', 'bunches', 'pieces', 'liters', 'gallons'],
            example: 'lbs'
          },
          stock: {
            type: 'number',
            format: 'decimal',
            description: 'Current stock amount',
            example: 50.0
          },
          min_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Minimum stock threshold',
            example: 20.0
          },
          max_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Maximum stock capacity',
            example: 100.0,
            nullable: true
          },
          cost_per_unit: {
            type: 'number',
            format: 'decimal',
            description: 'Cost per unit in USD',
            example: 2.99
          },
          supplier: {
            type: 'string',
            description: 'Supplier name',
            example: 'Asian Grocery Co.',
            nullable: true
          },
          location: {
            type: 'string',
            description: 'Storage location',
            example: 'Dry Storage A',
            nullable: true
          },
          created_at: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp'
          },
          updated_at: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp'
          }
        },
        required: ['id', 'name', 'category', 'unit', 'stock', 'min_stock', 'cost_per_unit', 'created_at', 'updated_at']
      },
      CreateIngredient: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Name of the ingredient',
            example: 'Jasmine Rice'
          },
          category: {
            type: 'string',
            description: 'Category of the ingredient',
            enum: ['All', 'Meat', 'Dry', 'Liquid', 'Bag', 'Vegetables', 'Dairy', 'Spices', 'Frozen'],
            example: 'Dry'
          },
          unit: {
            type: 'string',
            description: 'Unit of measurement',
            enum: ['lbs', 'kg', 'oz', 'grams', 'bottles', 'cans', 'bunches', 'pieces', 'liters', 'gallons'],
            example: 'lbs'
          },
          stock: {
            type: 'number',
            format: 'decimal',
            description: 'Current stock amount',
            example: 50.0
          },
          min_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Minimum stock threshold',
            example: 20.0
          },
          max_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Maximum stock capacity',
            example: 100.0,
            nullable: true
          },
          cost_per_unit: {
            type: 'number',
            format: 'decimal',
            description: 'Cost per unit in USD',
            example: 2.99
          },
          supplier: {
            type: 'string',
            description: 'Supplier name',
            example: 'Asian Grocery Co.',
            nullable: true
          },
          location: {
            type: 'string',
            description: 'Storage location',
            example: 'Dry Storage A',
            nullable: true
          }
        },
        required: ['name', 'category', 'unit', 'stock', 'min_stock', 'cost_per_unit']
      },
      UpdateIngredient: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Name of the ingredient',
            example: 'Jasmine Rice'
          },
          category: {
            type: 'string',
            description: 'Category of the ingredient',
            enum: ['All', 'Meat', 'Dry', 'Liquid', 'Bag', 'Vegetables', 'Dairy', 'Spices', 'Frozen'],
            example: 'Dry'
          },
          unit: {
            type: 'string',
            description: 'Unit of measurement',
            enum: ['lbs', 'kg', 'oz', 'grams', 'bottles', 'cans', 'bunches', 'pieces', 'liters', 'gallons'],
            example: 'lbs'
          },
          stock: {
            type: 'number',
            format: 'decimal',
            description: 'Current stock amount',
            example: 50.0
          },
          min_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Minimum stock threshold',
            example: 20.0
          },
          max_stock: {
            type: 'number',
            format: 'decimal',
            description: 'Maximum stock capacity',
            example: 100.0,
            nullable: true
          },
          cost_per_unit: {
            type: 'number',
            format: 'decimal',
            description: 'Cost per unit in USD',
            example: 2.99
          },
          supplier: {
            type: 'string',
            description: 'Supplier name',
            example: 'Asian Grocery Co.',
            nullable: true
          },
          location: {
            type: 'string',
            description: 'Storage location',
            example: 'Dry Storage A',
            nullable: true
          }
        }
      },
      Supplier: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the supplier'
          },
          name: {
            type: 'string',
            description: 'Supplier company name',
            example: 'Fresh Farm Produce'
          },
          contact_person: {
            type: 'string',
            description: 'Primary contact person',
            example: 'John Smith',
            nullable: true
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Contact email address',
            example: '<EMAIL>',
            nullable: true
          },
          phone: {
            type: 'string',
            description: 'Contact phone number',
            example: '(*************',
            nullable: true
          },
          address: {
            type: 'string',
            description: 'Business address',
            example: '123 Farm Road, Valley City',
            nullable: true
          },
          notes: {
            type: 'string',
            description: 'Additional notes about the supplier',
            example: 'Reliable supplier for fresh ingredients',
            nullable: true
          },
          is_active: {
            type: 'boolean',
            description: 'Whether the supplier is currently active',
            example: true
          },
          created_at: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp'
          },
          updated_at: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp'
          }
        },
        required: ['id', 'name', 'is_active', 'created_at', 'updated_at']
      },
      CreateSupplier: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Supplier company name',
            example: 'Fresh Farm Produce'
          },
          contact_person: {
            type: 'string',
            description: 'Primary contact person',
            example: 'John Smith',
            nullable: true
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Contact email address',
            example: '<EMAIL>',
            nullable: true
          },
          phone: {
            type: 'string',
            description: 'Contact phone number',
            example: '(*************',
            nullable: true
          },
          address: {
            type: 'string',
            description: 'Business address',
            example: '123 Farm Road, Valley City',
            nullable: true
          },
          notes: {
            type: 'string',
            description: 'Additional notes about the supplier',
            example: 'Reliable supplier for fresh ingredients',
            nullable: true
          },
          is_active: {
            type: 'boolean',
            description: 'Whether the supplier is currently active',
            example: true,
            default: true
          }
        },
        required: ['name']
      },
      Error: {
        type: 'object',
        properties: {
          error: {
            type: 'string',
            description: 'Error message',
            example: 'Database not available'
          }
        },
        required: ['error']
      }
    }
  }
};
