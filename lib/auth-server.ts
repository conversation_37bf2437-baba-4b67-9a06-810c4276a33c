import { createServerSupabaseClient } from './supabase';
import { UserProfile, UserRole } from './auth-types';

// Server-side authentication utilities
export async function getServerUser() {
  const supabase = await createServerSupabaseClient();
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return { user: null, profile: null };
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return { user, profile: null };
    }

    return { user, profile };
  } catch (error) {
    console.error('Error in getServerUser:', error);
    return { user: null, profile: null };
  }
}

export async function requireAuth() {
  const { user, profile } = await getServerUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  return { user, profile };
}

export async function requireRole(roles: UserRole | UserRole[]) {
  const { user, profile } = await requireAuth();
  
  if (!profile) {
    throw new Error('User profile not found');
  }
  
  const roleArray = Array.isArray(roles) ? roles : [roles];
  
  if (!roleArray.includes(profile.role)) {
    throw new Error(`Access denied. Required role: ${roleArray.join(' or ')}`);
  }
  
  return { user, profile };
}

export async function requireAdmin() {
  return requireRole('admin');
}

export async function requireStaffOrAdmin() {
  return requireRole(['staff', 'admin']);
}
