import { createClient } from '@supabase/supabase-js';
import { createBrowserClient, createServerClient } from '@supabase/ssr';

// Use the new database configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Supabase configuration:');
console.log('- URL:', supabaseUrl ? 'Present' : 'Missing');
console.log('- Anon Key:', supabaseAnonKey ? 'Present' : 'Missing');
console.log('- Service Key:', supabaseServiceKey ? 'Present' : 'Missing');
console.log('- Service Key Value:', supabaseServiceKey ? 'Has value' : 'No value');
console.log('- Environment:', typeof window !== 'undefined' ? 'Browser' : 'Server');

// Client for browser/client-side operations
export const supabase = supabaseUrl && supabaseAnonKey
  ? createBrowserClient(supabaseUrl, supabaseAnonKey)
  : null;

// Server client for SSR operations
export async function createServerSupabaseClient() {
  const { cookies } = await import('next/headers');
  const cookieStore = await cookies();

  return createServerClient(
    supabaseUrl!,
    supabaseAnonKey!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

// Admin client for server-side operations with elevated permissions
export const supabaseAdmin = supabaseUrl && supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null;

// Database schema types
export interface CachedOrder {
  id: string;
  square_order_id: string;
  location_id: string;
  created_at: string;
  updated_at: string;
  state: string;
  total_money: number;
  total_tax_money?: number;
  total_discount_money?: number;
  line_items: Record<string, unknown>[];
  fulfillments?: Record<string, unknown>[];
  tenders?: Record<string, unknown>[];
  cached_at: string;
}

export interface CachedProduct {
  id: string;
  square_catalog_id: string;
  name: string;
  category?: string;
  price: number;
  description?: string;
  image_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  cached_at: string;
}

export interface SalesMetrics {
  id: string;
  date: string;
  location_id: string;
  total_sales: number;
  total_orders: number;
  average_order_value: number;
  total_tax: number;
  total_tips: number;
  created_at: string;
}

// Helper functions for database operations
export async function cacheOrder(order: Record<string, unknown>) {
  if (!supabaseAdmin) {
    console.warn('Supabase admin client not available, skipping order caching');
    return null;
  }

  // Ensure location_id is never null - Square API uses snake_case field names
  const locationId = order.location_id || order.locationId || process.env.SQUARE_LOCATION_ID;

  if (!locationId) {
    console.warn('No location ID available for order, skipping cache:', order.id);
    return null;
  }

  // Ensure required timestamp fields are not null - Square API uses snake_case field names
  const createdAt = order.created_at || order.createdAt || new Date().toISOString();
  const updatedAt = order.updated_at || order.updatedAt || createdAt;

  const { data, error } = await supabaseAdmin
    .from('cached_orders')
    .upsert({
      square_order_id: order.id,
      location_id: locationId,
      created_at: createdAt,
      updated_at: updatedAt,
      state: order.state,
      total_money: order.totalMoney?.amount || 0,
      total_tax_money: order.totalTaxMoney?.amount || 0,
      total_discount_money: order.totalDiscountMoney?.amount || 0,
      line_items: order.lineItems || [],
      fulfillments: order.fulfillments || [],
      tenders: order.tenders || [],
      cached_at: new Date().toISOString(),
    }, {
      onConflict: 'square_order_id'
    });

  if (error) {
    console.error('Error caching order:', error);
    throw error;
  }

  return data;
}

export async function getCachedOrders(startDate: string, endDate: string) {
  if (!supabaseAdmin) {
    console.warn('Supabase admin client not available, returning empty array');
    return [];
  }

  const { data, error } = await supabaseAdmin
    .from('cached_orders')
    .select('*')
    .gte('created_at', startDate)
    .lte('created_at', endDate)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching cached orders:', error);
    throw error;
  }

  return data;
}

export async function cacheSalesMetrics(metrics: Omit<SalesMetrics, 'id' | 'created_at'>) {
  if (!supabaseAdmin) {
    console.warn('Supabase admin client not available, skipping metrics caching');
    return null;
  }

  const { data, error } = await supabaseAdmin
    .from('sales_metrics')
    .upsert({
      ...metrics,
      created_at: new Date().toISOString(),
    }, {
      onConflict: 'date,location_id'
    });

  if (error) {
    console.error('Error caching sales metrics:', error);
    throw error;
  }

  return data;
}

export async function getSalesMetrics(startDate: string, endDate: string) {
  if (!supabaseAdmin) {
    console.warn('Supabase admin client not available, returning empty array');
    return [];
  }

  const { data, error } = await supabaseAdmin
    .from('sales_metrics')
    .select('*')
    .gte('date', startDate)
    .lte('date', endDate)
    .order('date', { ascending: true });

  if (error) {
    console.error('Error fetching sales metrics:', error);
    throw error;
  }

  return data;
}
