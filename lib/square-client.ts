// Square API configuration using REST API instead of SDK
const SQUARE_BASE_URL = process.env.SQUARE_ENVIRONMENT === 'production'
  ? 'https://connect.squareup.com'
  : 'https://connect.squareupsandbox.com';

console.log('Square API configuration:');
console.log('- Base URL:', SQUARE_BASE_URL);
console.log('- SQUARE_ACCESS_TOKEN:', process.env.SQUARE_ACCESS_TOKEN ? 'Present' : 'Missing');
console.log('- SQUARE_ENVIRONMENT:', process.env.SQUARE_ENVIRONMENT || 'Not set');
console.log('- SQUARE_LOCATION_ID:', process.env.SQUARE_LOCATION_ID ? 'Present' : 'Missing');

// Square API client using fetch
export const squareApi = {
  async checkPermissions() {
    console.log('Checking Square API permissions...');

    try {
      // Try to get locations (basic permission)
      const locationsResponse = await fetch(`${SQUARE_BASE_URL}/v2/locations`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SQUARE_ACCESS_TOKEN}`,
          'Content-Type': 'application/json',
          'Square-Version': '2025-06-18',
        },
      });

      if (locationsResponse.ok) {
        console.log('✅ LOCATIONS_READ permission: Available');
        const locations = await locationsResponse.json();
        console.log('Available locations:', locations.locations?.map((l: { name: string }) => l.name) || []);
      } else {
        console.log('❌ LOCATIONS_READ permission: Not available');
      }

    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  },

  async searchOrders(request: Record<string, unknown>) {
    if (!process.env.SQUARE_ACCESS_TOKEN) {
      throw new Error('Square access token not configured');
    }

    const response = await fetch(`${SQUARE_BASE_URL}/v2/orders/search`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SQUARE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
        'Square-Version': '2025-06-18',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Square API error details:', errorText);
      throw new Error(`Square API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  },

  async searchPayments(locationId: string, beginTime?: string, endTime?: string) {
    if (!process.env.SQUARE_ACCESS_TOKEN) {
      throw new Error('Square access token not configured');
    }

    // Build query parameters
    const params = new URLSearchParams();
    if (locationId) params.append('location_id', locationId);
    if (beginTime) params.append('begin_time', beginTime);
    if (endTime) params.append('end_time', endTime);
    params.append('sort_order', 'DESC');
    params.append('limit', '100');

    const response = await fetch(`${SQUARE_BASE_URL}/v2/payments?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.SQUARE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
        'Square-Version': '2024-07-17',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Square API error details:', errorText);
      throw new Error(`Square API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  },

  async listCatalog() {
    if (!process.env.SQUARE_ACCESS_TOKEN) {
      throw new Error('Square access token not configured');
    }

    const response = await fetch(`${SQUARE_BASE_URL}/v2/catalog/list`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.SQUARE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
        'Square-Version': '2025-06-18',
      },
    });

    if (!response.ok) {
      throw new Error(`Square API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }
};

export const SQUARE_LOCATION_ID = process.env.SQUARE_LOCATION_ID!;

// Types for Square data
export interface SquareOrder {
  id: string;
  locationId: string;
  createdAt: string;
  updatedAt: string;
  state: string;
  totalMoney: {
    amount: number;
    currency: string;
  };
  totalTaxMoney?: {
    amount: number;
    currency: string;
  };
  totalDiscountMoney?: {
    amount: number;
    currency: string;
  };
  lineItems: SquareLineItem[];
  fulfillments?: SquareFulfillment[];
  tenders?: SquareTender[];
}

export interface SquareLineItem {
  uid: string;
  name: string;
  quantity: string;
  catalogObjectId?: string;
  variationName?: string;
  basePriceMoney: {
    amount: number;
    currency: string;
  };
  totalMoney: {
    amount: number;
    currency: string;
  };
}

export interface SquareFulfillment {
  uid: string;
  type: string;
  state: string;
  pickupDetails?: {
    recipient?: {
      displayName: string;
    };
    pickupAt?: string;
  };
}

export interface SquareTender {
  id: string;
  type: string;
  amountMoney: {
    amount: number;
    currency: string;
  };
  tipMoney?: {
    amount: number;
    currency: string;
  };
  cardDetails?: {
    status: string;
    card: {
      cardBrand: string;
      last4: string;
    };
  };
}

export interface SalesReportData {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  totalTax: number;
  totalTips: number;
  topProducts: Array<{
    name: string;
    quantity: number;
    revenue: number;
  }>;
  salesByDay: Array<{
    date: string;
    sales: number;
    orders: number;
  }>;
}

// Helper function to convert Square money amount (cents) to dollars
export function formatSquareMoney(amount: number): number {
  return amount / 100;
}

// Helper function to format currency
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}
