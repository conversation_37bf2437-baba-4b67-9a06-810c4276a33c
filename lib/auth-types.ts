// Authentication types for the inventory management system

export type UserRole = 'admin' | 'staff' | 'viewer';

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthUser {
  id: string;
  email: string;
  profile?: UserProfile;
}

export interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  hasRole: (role: UserRole | UserRole[]) => boolean;
  canAccess: (feature: Permission) => boolean;
}

// Feature permissions based on roles
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    'view_dashboard',
    'view_products',
    'manage_products',
    'view_ingredients',
    'manage_ingredients',
    'view_reports',
    'sync_square',
    'manage_users'
  ],
  staff: [
    'view_dashboard',
    'view_products',
    'manage_products',
    'view_ingredients',
    'manage_ingredients'
  ],
  viewer: [
    'view_dashboard',
    'view_products',
    'view_ingredients'
  ]
};

// Define all possible permissions explicitly
export type Permission =
  | 'view_dashboard'
  | 'view_products'
  | 'manage_products'
  | 'view_ingredients'
  | 'manage_ingredients'
  | 'view_reports'
  | 'sync_square'
  | 'manage_users';
