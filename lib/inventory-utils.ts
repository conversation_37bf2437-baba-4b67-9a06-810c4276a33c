import { Ingredient, Product, StockStatus } from './types';

/**
 * Determines the stock status based on current stock and thresholds
 */
export function getStockStatus(
  currentStock: number,
  minStock: number,
  maxStock?: number
): StockStatus {
  if (currentStock === 0) return 'out';
  if (currentStock <= minStock) return 'low';
  if (maxStock && currentStock >= maxStock * 0.8) return 'high';
  return 'normal';
}

/**
 * Gets the appropriate color class for stock status
 */
export function getStockStatusColor(status: StockStatus): string {
  switch (status) {
    case 'out':
      return 'text-red-800 bg-red-100';
    case 'low':
      return 'text-red-700 bg-red-50';
    case 'high':
      return 'text-green-700 bg-green-50';
    case 'normal':
    default:
      return 'text-blue-700 bg-blue-50';
  }
}

/**
 * Gets the appropriate border color for stock status
 */
export function getStockStatusBorderColor(status: StockStatus): string {
  switch (status) {
    case 'out':
    case 'low':
      return 'border-red-300 bg-red-50';
    case 'high':
      return 'border-green-300 bg-green-50';
    case 'normal':
    default:
      return 'border-gray-200';
  }
}

/**
 * Calculates days until expiry
 * Uses a fixed reference date for SSR compatibility
 */
export function getDaysUntilExpiry(expiryDate: string): number {
  // Use a fixed reference date for consistent SSR rendering
  const referenceDate = new Date('2024-01-13T14:30:00.000Z');
  const expiry = new Date(expiryDate);
  const diffTime = expiry.getTime() - referenceDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

/**
 * Checks if an ingredient is expiring soon (within specified days)
 */
export function isExpiringSoon(expiryDate: string, withinDays: number = 3): boolean {
  return getDaysUntilExpiry(expiryDate) <= withinDays;
}

/**
 * Calculates profit margin for a product
 */
export function calculateProfitMargin(price: number, cost: number): number {
  if (price === 0) return 0;
  return ((price - cost) / price) * 100;
}

/**
 * Calculates total inventory value for ingredients
 */
export function calculateInventoryValue(ingredients: Ingredient[]): number {
  return ingredients.reduce((total, ingredient) => {
    return total + (ingredient.stock * ingredient.costPerUnit);
  }, 0);
}

/**
 * Calculates total potential profit for products
 */
export function calculateTotalPotentialProfit(products: Product[]): number {
  return products.reduce((total, product) => {
    return total + (product.price - product.cost);
  }, 0);
}

/**
 * Filters products based on criteria
 */
export function filterProducts(
  products: Product[],
  filters: {
    category?: string;
    status?: string;
    stockStatus?: StockStatus;
    search?: string;
  }
): Product[] {
  return products.filter((product) => {
    // Category filter
    if (filters.category && filters.category !== 'All' && product.category !== filters.category) {
      return false;
    }

    // Status filter
    if (filters.status && product.status !== filters.status) {
      return false;
    }

    // Stock status filter
    if (filters.stockStatus) {
      const stockStatus = getStockStatus(product.stock, product.minStock);
      if (stockStatus !== filters.stockStatus) {
        return false;
      }
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const matchesName = product.name.toLowerCase().includes(searchTerm);
      const matchesDescription = product.description.toLowerCase().includes(searchTerm);
      const matchesIngredients = product.ingredients.some(ingredient =>
        ingredient.toLowerCase().includes(searchTerm)
      );
      
      if (!matchesName && !matchesDescription && !matchesIngredients) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Filters ingredients based on criteria
 */
export function filterIngredients(
  ingredients: Ingredient[],
  filters: {
    category?: string;
    stockStatus?: StockStatus;
    expiringWithinDays?: number;
    supplier?: string;
    search?: string;
  }
): Ingredient[] {
  return ingredients.filter((ingredient) => {
    // Category filter
    if (filters.category && filters.category !== 'All' && ingredient.category !== filters.category) {
      return false;
    }

    // Stock status filter
    if (filters.stockStatus) {
      const stockStatus = getStockStatus(ingredient.stock, ingredient.minStock, ingredient.maxStock);
      if (stockStatus !== filters.stockStatus) {
        return false;
      }
    }

    // Expiring soon filter
    if (filters.expiringWithinDays !== undefined) {
      const daysUntilExpiry = getDaysUntilExpiry(ingredient.expiryDate);
      if (daysUntilExpiry > filters.expiringWithinDays) {
        return false;
      }
    }

    // Supplier filter
    if (filters.supplier && filters.supplier !== 'All' && ingredient.supplier !== filters.supplier) {
      return false;
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const matchesName = ingredient.name.toLowerCase().includes(searchTerm);
      const matchesSupplier = ingredient.supplier.toLowerCase().includes(searchTerm);
      const matchesLocation = ingredient.location.toLowerCase().includes(searchTerm);
      
      if (!matchesName && !matchesSupplier && !matchesLocation) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Sorts an array by a specified field and direction
 */
export function sortItems<T>(
  items: T[],
  field: keyof T,
  direction: 'asc' | 'desc' = 'asc'
): T[] {
  return [...items].sort((a, b) => {
    const aValue = a[field];
    const bValue = b[field];

    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Formats currency values
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

/**
 * Formats dates in a readable format
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Formats relative time (e.g., "2 hours ago")
 * Uses a fixed reference time for SSR compatibility
 */
export function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString);
  // Use a fixed reference time for consistent SSR rendering
  const referenceTime = new Date('2024-01-13T14:30:00.000Z');
  const diffInSeconds = Math.floor((referenceTime.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;

  return formatDate(dateString);
}

/**
 * Generates a unique ID (simple implementation)
 */
export function generateId(): number {
  return Date.now() + Math.floor(Math.random() * 1000);
}
