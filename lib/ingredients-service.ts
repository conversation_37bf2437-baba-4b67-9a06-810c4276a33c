// Client-side service that calls API routes
// All database operations are handled server-side

export interface Ingredient {
  id: string;
  name: string;
  category: string;
  unit: string;
  stock: number;
  min_stock: number;
  max_stock?: number;
  cost_per_unit: number;
  supplier?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface PriceHistory {
  id: string;
  ingredient_id: string;
  cost_per_unit: number;
  supplier?: string;
  purchase_date: string;
  quantity_purchased?: number;
  total_cost?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface PriceStats {
  lowest_price: number;
  highest_price: number;
  average_price: number;
  latest_price: number;
  price_trend: 'increasing' | 'decreasing' | 'stable' | 'insufficient_data' | 'no_data';
  total_purchases: number;
}

export interface WeeklyPriceSummary {
  ingredient_id: string;
  ingredient_name: string;
  week_start: string;
  lowest_price: number;
  highest_price: number;
  average_price: number;
  purchase_count: number;
}

export interface RestockData {
  ingredientId: string;
  ingredientName: string;
  currentStock: number;
  addAmount: number;
  newStock: number;
  unit: string;
  costPerUnit: number;
  totalCost: number;
  supplier: string;
  notes: string;
  restockDate: string;
}

class IngredientsService {
  // CRUD Operations for Ingredients
  async getAllIngredients(): Promise<Ingredient[]> {
    try {
      console.log('Client: Fetching ingredients from API');
      const response = await fetch('/api/ingredients');

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        return [];
      }

      const data = await response.json();
      console.log('Client: Received ingredients:', data.length);
      return data;
    } catch (error) {
      console.error('Client error fetching ingredients:', error);
      return [];
    }
  }

  async getIngredientById(id: string): Promise<Ingredient | null> {
    try {
      const response = await fetch(`/api/ingredients/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error:', errorData);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Client error fetching ingredient:', error);
      return null;
    }
  }

  async createIngredient(ingredient: Omit<Ingredient, 'id' | 'created_at' | 'updated_at'>): Promise<Ingredient> {
    try {
      const response = await fetch('/api/ingredients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(ingredient),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create ingredient');
      }

      return await response.json();
    } catch (error) {
      console.error('Client error creating ingredient:', error);
      throw error;
    }
  }

  async updateIngredient(id: string, updates: Partial<Ingredient>): Promise<Ingredient> {
    try {
      console.log('Client: Updating ingredient via API:', { id, updates });

      const response = await fetch(`/api/ingredients/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update ingredient');
      }

      const data = await response.json();
      console.log('Client: Updated ingredient:', data);
      return data;
    } catch (error) {
      console.error('Client error updating ingredient:', error);
      throw error;
    }
  }

  async deleteIngredient(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/ingredients/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete ingredient');
      }
    } catch (error) {
      console.error('Client error deleting ingredient:', error);
      throw error;
    }
  }

  // Restock operation - updates stock and creates price history
  async restockIngredient(restockData: RestockData): Promise<{ ingredient: Ingredient; priceHistory: PriceHistory }> {
    try {
      const response = await fetch(`/api/ingredients/${restockData.ingredientId}/restock`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(restockData)
      });

      if (!response.ok) {
        throw new Error('Failed to restock ingredient');
      }

      return await response.json();
    } catch (error) {
      console.error('Error restocking ingredient:', error);
      throw error;
    }
  }

  // Price History Operations
  async getPriceHistory(ingredientId: string, limit: number = 10): Promise<PriceHistory[]> {
    try {
      const response = await fetch(`/api/ingredients/${ingredientId}/price-history?limit=${limit}`);
      if (!response.ok) {
        throw new Error('Failed to fetch price history');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching price history:', error);
      return [];
    }
  }

  async getPriceStats(_ingredientId: string, _daysBack: number = 30): Promise<PriceStats> {
    // TODO: Implement API endpoint for price stats
    return {
      lowest_price: 0,
      highest_price: 0,
      average_price: 0,
      latest_price: 0,
      price_trend: 'no_data',
      total_purchases: 0
    };
  }

  async getWeeklyPriceSummary(_ingredientId?: string): Promise<WeeklyPriceSummary[]> {
    // TODO: Implement API endpoint for weekly price summary
    return [];
  }

  // Get lowest price for a specific time period
  async getLowestPriceInPeriod(_ingredientId: string, _days: number = 7): Promise<{ price: number; date: string; supplier?: string } | null> {
    // TODO: Implement API endpoint for lowest price
    return null;
  }

  // Get price trend analysis
  async getPriceTrendAnalysis(_ingredientId: string): Promise<{
    currentPrice: number;
    weeklyLow: number;
    monthlyLow: number;
    trend: string;
    savings: number;
    recommendation: string;
  }> {
    // TODO: Implement API endpoint for price trend analysis
    return {
      currentPrice: 0,
      weeklyLow: 0,
      monthlyLow: 0,
      trend: 'no_data',
      savings: 0,
      recommendation: 'Feature not implemented'
    };
  }

  // Search ingredients
  async searchIngredients(query: string): Promise<Ingredient[]> {
    try {
      const response = await fetch(`/api/ingredients?search=${encodeURIComponent(query)}`);
      if (!response.ok) {
        throw new Error('Failed to search ingredients');
      }
      return await response.json();
    } catch (error) {
      console.error('Error searching ingredients:', error);
      return [];
    }
  }

  // Get ingredients by category
  async getIngredientsByCategory(category: string): Promise<Ingredient[]> {
    try {
      const response = await fetch(`/api/ingredients?category=${encodeURIComponent(category)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch ingredients by category');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching ingredients by category:', error);
      return [];
    }
  }

  // Get low stock ingredients
  async getLowStockIngredients(): Promise<Ingredient[]> {
    try {
      const response = await fetch('/api/ingredients?lowStock=true');
      if (!response.ok) {
        throw new Error('Failed to fetch low stock ingredients');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching low stock ingredients:', error);
      return [];
    }
  }
}

export const ingredientsService = new IngredientsService();
