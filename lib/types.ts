// Shared types for the inventory management system

export interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  ingredients: string[];
  description: string;
  status: 'active' | 'inactive' | 'discontinued';
  image?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Ingredient {
  id: string;
  name: string;
  category: string;
  unit: string;
  stock: number;
  minStock: number;
  maxStock: number;
  costPerUnit: number;
  supplier: string;
  lastRestocked: string;
  expiryDate: string;
  location: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface DashboardStats {
  totalProducts: number;
  lowStockProducts: number;
  totalIngredients: number;
  lowStockIngredients: number;
  todaysSales: number;
  weeklyRevenue: number;
  monthlyRevenue?: number;
  totalInventoryValue: number;
}

export interface LowStockAlert {
  id: number;
  name: string;
  type: 'product' | 'ingredient';
  stock: number;
  unit: string;
  minStock: number;
  severity: 'low' | 'critical' | 'out';
}

export interface ActivityLog {
  id: number;
  action: string;
  item: string;
  itemType: 'product' | 'ingredient';
  quantity?: number;
  user?: string;
  timestamp: string;
  details?: string;
}

export interface StockMovement {
  id: number;
  itemId: number;
  itemName: string;
  itemType: 'product' | 'ingredient';
  movementType: 'in' | 'out' | 'adjustment' | 'waste';
  quantity: number;
  unit: string;
  reason: string;
  user: string;
  timestamp: string;
  cost?: number;
}

export interface Supplier {
  id: number;
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
  rating: number;
  isActive: boolean;
}

export interface Recipe {
  id: number;
  productId: number;
  ingredients: RecipeIngredient[];
  instructions: string[];
  prepTime: number; // in minutes
  servings: number;
  notes?: string;
}

export interface RecipeIngredient {
  ingredientId: number;
  ingredientName: string;
  quantity: number;
  unit: string;
  notes?: string;
}

// Utility types
export type StockStatus = 'low' | 'normal' | 'high' | 'out';
export type ProductCategory = 'Main Course' | 'Appetizer' | 'Salad' | 'Beverage' | 'Dessert' | 'Side';
export type IngredientCategory = 'Meat' | 'Vegetables' | 'Dairy' | 'Beverages' | 'Pantry' | 'Spices' | 'Frozen';

// Filter and sort options
export interface ProductFilters {
  category?: ProductCategory;
  status?: Product['status'];
  stockStatus?: StockStatus;
  search?: string;
}

export interface IngredientFilters {
  category?: IngredientCategory;
  stockStatus?: StockStatus;
  expiringWithinDays?: number;
  supplier?: string;
  search?: string;
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
}

// API response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface ProductFormData {
  name: string;
  category: ProductCategory;
  price: number;
  cost: number;
  minStock: number;
  description: string;
  ingredients: string[];
  status: Product['status'];
}

export interface IngredientFormData {
  name: string;
  category: IngredientCategory;
  unit: string;
  minStock: number;
  maxStock: number;
  costPerUnit: number;
  supplier: string;
  location: string;
  expiryDate: string;
}

// Validation schemas (for use with form libraries)
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState {
  isSubmitting: boolean;
  errors: ValidationError[];
  success: boolean;
}
