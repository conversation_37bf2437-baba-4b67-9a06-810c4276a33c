// Type declarations for squareup package
declare module 'squareup' {
  export enum Environment {
    Production = 'production',
    Sandbox = 'sandbox',
  }

  export interface ClientOptions {
    accessToken: string;
    environment: Environment;
  }

  export interface Money {
    amount: number;
    currency: string;
  }

  export interface OrderLineItem {
    uid: string;
    name: string;
    quantity: string;
    catalogObjectId?: string;
    variationName?: string;
    basePriceMoney: Money;
    totalMoney: Money;
  }

  export interface OrderFulfillment {
    uid: string;
    type: string;
    state: string;
    pickupDetails?: {
      recipient?: {
        displayName: string;
      };
      pickupAt?: string;
    };
  }

  export interface OrderTender {
    id: string;
    type: string;
    amountMoney: Money;
    tipMoney?: Money;
    cardDetails?: {
      status: string;
      card: {
        cardBrand: string;
        last4: string;
      };
    };
  }

  export interface Order {
    id: string;
    locationId: string;
    createdAt: string;
    updatedAt: string;
    state: string;
    totalMoney?: Money;
    totalTaxMoney?: Money;
    totalDiscountMoney?: Money;
    lineItems?: OrderLineItem[];
    fulfillments?: OrderFulfillment[];
    tenders?: OrderTender[];
  }

  export interface SearchOrdersRequest {
    locationIds: string[];
    query?: {
      filter?: {
        dateTimeFilter?: {
          createdAt?: {
            startAt: string;
            endAt: string;
          };
        };
        stateFilter?: {
          states: string[];
        };
      };
      sort?: {
        sortField: string;
        sortOrder: string;
      };
    };
    limit?: number;
  }

  export interface SearchOrdersResponse {
    orders?: Order[];
  }

  export interface ListCatalogResponse {
    objects?: any[];
  }

  export interface OrdersApi {
    searchOrders(request: SearchOrdersRequest): Promise<{ result: SearchOrdersResponse }>;
  }

  export interface PaymentsApi {
    // Add payment methods as needed
  }

  export interface CatalogApi {
    listCatalog(cursor?: string, types?: string): Promise<{ result: ListCatalogResponse }>;
  }

  export interface LocationsApi {
    // Add location methods as needed
  }

  export interface InventoryApi {
    // Add inventory methods as needed
  }

  export class Client {
    constructor(options: ClientOptions);
    ordersApi: OrdersApi;
    paymentsApi: PaymentsApi;
    catalogApi: CatalogApi;
    locationsApi: LocationsApi;
    inventoryApi: InventoryApi;
  }
}
