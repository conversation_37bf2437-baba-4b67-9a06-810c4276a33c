# Supabase Authentication Setup Guide

## Overview
This guide will help you set up server-side authentication using Supabase for your inventory management system.

## Prerequisites
- A Supabase account and project
- Node.js and npm/yarn installed
- Basic understanding of Next.js and React

## Step 1: Supabase Project Setup

### 1.1 Create a Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign in and create a new project
3. Wait for the project to be fully provisioned

### 1.2 Get Your Project Credentials
1. Go to Project Settings > API
2. Copy the following values:
   - Project URL
   - Anon (public) key
   - Service role key (keep this secret!)

## Step 2: Environment Variables

### 2.1 Create Environment File
Copy `.env.example` to `.env.local`:
```bash
cp .env.example .env.local
```

### 2.2 Configure Environment Variables
Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 3: Database Setup

### 3.1 Run the Schema
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Run the `supabase-schema.sql` file to create all necessary tables

### 3.2 Enable Row Level Security
The schema automatically enables RLS policies for secure data access.

## Step 4: Create Your First Admin User

### 4.1 Create User in Supabase Dashboard
1. Go to Authentication > Users in your Supabase dashboard
2. Click "Add user"
3. Enter your email and password
4. Click "Create user"

### 4.2 Set Admin Role
Run this SQL in the Supabase SQL Editor:
```sql
UPDATE user_profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## Step 5: Test the Authentication

### 5.1 Start the Development Server
```bash
npm run dev
```

### 5.2 Test Login
1. Navigate to `http://localhost:3000`
2. You should be redirected to `/login`
3. Enter your credentials
4. You should be redirected to the dashboard

## User Roles and Permissions

### Admin
- Full system access
- Can manage products and ingredients
- Access to reports and Square sync
- Can manage other users (future feature)

### Staff
- Can manage inventory (products and ingredients)
- Cannot access reports or Square sync
- Cannot manage users

### Viewer
- Read-only access to products and ingredients
- Cannot modify any data
- Cannot access admin features

## Security Features

### Server-Side Protection
- Middleware protects all routes
- API routes require authentication
- Role-based access control

### Database Security
- Row Level Security (RLS) policies
- User profiles automatically created
- Secure session management

## Troubleshooting

### Common Issues

1. **"Authentication required" errors**
   - Check environment variables are set correctly
   - Verify Supabase project is active
   - Check browser network tab for API errors

2. **User profile not found**
   - Ensure the user_profiles table exists
   - Check if the user was created properly
   - Verify RLS policies are enabled

3. **Redirect loops**
   - Clear browser cookies and localStorage
   - Check middleware configuration
   - Verify environment variables

### Debug Mode
Add this to your `.env.local` for debugging:
```env
NEXT_PUBLIC_DEBUG_AUTH=true
```

## Production Deployment

### Vercel Environment Variables
Set these in your Vercel dashboard:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

### Security Checklist
- [ ] Service role key is kept secret
- [ ] RLS policies are enabled
- [ ] HTTPS is enforced in production
- [ ] Environment variables are properly set

## Next Steps
- Set up email confirmation (optional)
- Configure social login providers (optional)
- Implement user management interface
- Set up audit logging

Your authentication system is now ready! 🎉
